# Dockerfile para hospedagem em nuvem
FROM node:18-slim

# Instalar dependências básicas
RUN apt-get update && apt-get install -y \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

# Criar diretório da aplicação
WORKDIR /app

# Copiar package.json e package-lock.json
COPY package*.json ./

# Instalar dependências
RUN npm ci --only=production

# Copiar código da aplicação
COPY . .

# Criar diretório para credenciais
RUN mkdir -p credentials

# Expor porta
EXPOSE 8080

# Comando para iniciar a aplicação
CMD ["npm", "start"]
