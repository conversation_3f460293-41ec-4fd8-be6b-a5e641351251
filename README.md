# WhatsApp Todo Bot com IA

Um bot inteligente para WhatsApp que ajuda você a gerenciar suas tarefas, fundamentos de vida e missões usando inteligência artificial Gemini, com sincronização automática no Google Calendar e armazenamento no Google Sheets.

## ✨ Funcionalidades

### 🎯 Gerenciamento Completo de Vida
- **📝 Tarefas (Todos)**: <PERSON>erencie tarefas diárias com datas, prioridades e lembretes
- **🏛️ Fundamentos**: Acompanhe até 5 pilares da sua vida com pontuação de 1-10
- **🎯 Missões**: Planeje objetivos de 3 meses com acompanhamento semanal

### 🤖 Inteligência Artificial
- **Gemini AI**: Processamento avançado de linguagem natural em português
- **Compreensão Contextual**: Entende comandos complexos e intenções
- **Análise Inteligente**: Extrai datas, prioridades e contexto automaticamente

### 🔗 Integrações Poderosas
- **📱 WhatsApp**: Interface natural via WhatsApp - sem apps adicionais
- **📊 Google Sheets**: Armazenamento e análise de dados em tempo real
- **📅 Google Calendar**: Sincronização automática de tarefas e missões com datas
- **🔄 Sincronização Total**: Mudanças refletidas instantaneamente em todas as plataformas

### 🇧🇷 Experiência Brasileira
- **Idioma Português**: Interface 100% em português brasileiro
- **Datas Inteligentes**: Entende "hoje", "amanhã", "próxima sexta", etc.
- **Cultura Local**: Adaptado para o contexto e linguagem brasileira

## 💬 Comandos

O bot entende linguagem natural em português. Exemplos de comandos:

### 📝 Tarefas
- `lembrar de comprar leite hoje`
- `adicionar reunião amanhã às 14h`
- `listar minhas tarefas`
- `marcar comprar leite como feito`
- `excluir tarefa da reunião`
- `mostrar tarefas atrasadas`

### 🏛️ Fundamentos
- `adicionar fundamento Saúde`
- `atualizar fundamento Saúde 8`
- `listar meus fundamentos`
- `fundamento Carreira 7`

### 🎯 Missões
- `criar missão Aprender Python`
- `missão Emagrecer 10kg em 3 meses`
- `atualizar progresso missão Python 50%`
- `listar minhas missões`

### 📊 Outros
- `ajuda`
- `estatísticas`
- `resumo`

## 🚀 Configuração Rápida

### 📋 Pré-requisitos

- Node.js 18+ instalado
- Projeto no Google Cloud com APIs habilitadas:
  - Google Sheets API
  - Google Calendar API
- Chave da API do Gemini (Google AI)
- Conta do WhatsApp

### 📦 Instalação

1. Clone este repositório:
   ```bash
   git clone <repository-url>
   cd WhatsappTodolist
   ```

2. Instale as dependências:
   ```bash
   npm install
   ```

3. Configure as variáveis de ambiente:
   ```bash
   cp .env.example .env
   # Edite o arquivo .env com suas credenciais
   ```

4. Configure as credenciais do Google:
   - Crie um projeto no Google Cloud Console
   - Habilite as APIs do Google Sheets e Calendar
   - Crie uma conta de serviço e baixe o arquivo JSON
   - Coloque o arquivo em `config/service-account-key.json`

5. Teste as integrações:
   ```bash
   node test-integrations.js
   ```

6. Inicie o bot:
   ```bash
   npm start
   ```

## ⚙️ Configuração Detalhada

### 🔑 Variáveis de Ambiente (.env)

```bash
# Gemini AI Configuration
GEMINI_API_KEY=your_gemini_api_key_here

# Google Sheets Configuration
GOOGLE_SHEETS_ID=your_google_sheets_id_here

# Google Service Account Key (JSON content as string or path to file)
GOOGLE_SERVICE_ACCOUNT_KEY_PATH=./config/service-account-key.json

# Google Calendar Configuration
GOOGLE_CALENDAR_ID=primary

# Bot Configuration
MAX_TODOS_PER_USER=100
DEFAULT_TIMEZONE=America/Sao_Paulo
```

### 🔧 Configuração do Google Cloud

1. **Criar Projeto no Google Cloud Console**
   - Acesse [Google Cloud Console](https://console.cloud.google.com/)
   - Crie um novo projeto ou selecione um existente

2. **Habilitar APIs**
   ```bash
   # Habilite as seguintes APIs:
   - Google Sheets API
   - Google Calendar API
   ```

3. **Criar Conta de Serviço**
   - Vá para "IAM & Admin" > "Service Accounts"
   - Clique em "Create Service Account"
   - Baixe o arquivo JSON das credenciais
   - Coloque em `config/service-account-key.json`

4. **Configurar Google Sheets**
   - Crie uma nova planilha no Google Sheets
   - Compartilhe com o email da conta de serviço (editor)
   - Copie o ID da planilha da URL

5. **Configurar Google Calendar**
   - Use "primary" para o calendário principal
   - Ou crie um calendário específico e use seu ID

### 🤖 Configuração do Gemini AI

1. Acesse [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Crie uma nova chave de API
3. Adicione a chave no arquivo `.env`

### 📱 Primeira Execução

1. Execute o teste de integração:
   ```bash
   node test-integrations.js
   ```

2. Se todos os testes passarem, inicie o bot:
   ```bash
   npm start
   ```

3. Escaneie o código QR com o WhatsApp para conectar

## 🛠️ Desenvolvimento

Para desenvolvimento com reinicialização automática:
```bash
npm run dev
```

Executar testes:
```bash
npm test
```

Executar testes em modo watch:
```bash
npm run test:watch
```

Testar integrações:
```bash
node test-integrations.js
```

## 📁 Estrutura do Projeto

```
src/
├── index.js                    # Ponto de entrada principal
├── bot/
│   └── whatsappBot.js         # Integração WhatsApp Web
├── ai/
│   └── aiProcessor.js         # Integração Gemini AI e NLP
├── calendar/
│   └── calendarService.js     # Integração Google Calendar API
├── sheets/
│   └── sheetsService.js       # Integração Google Sheets API
├── todo/
│   └── todoManager.js         # Gerenciamento de tarefas
├── foundation/
│   └── foundationManager.js   # Gerenciamento de fundamentos
├── quest/
│   └── questManager.js        # Gerenciamento de missões
└── utils/
    ├── dateParser.js          # Análise de datas em linguagem natural
    ├── logger.js              # Utilitário de logging
    └── messages.js            # Mensagens em português

tests/                         # Arquivos de teste
config/                        # Arquivos de configuração
logs/                          # Arquivos de log (criados em tempo de execução)
test-integrations.js           # Teste das integrações
```

## 🔄 Como Funciona

1. **Recepção de Mensagem**: WhatsApp Web recebe mensagens via `whatsapp-web.js`
2. **Processamento IA**: Gemini AI analisa a mensagem para entender a intenção
3. **Execução de Ação**: Os gerenciadores executam a ação solicitada
4. **Sincronização**: Mudanças são sincronizadas automaticamente:
   - Google Sheets (armazenamento de dados)
   - Google Calendar (eventos com datas)
5. **Resposta**: Mensagem de confirmação é enviada ao usuário

## 📊 Estrutura de Dados

### Google Sheets - 4 Abas:
- **Todos**: Tarefas diárias com datas e prioridades
- **Interactions**: Log de todas as interações
- **Foundations**: Fundamentos de vida (máx. 5 por usuário)
- **Quests**: Missões de 3 meses com progresso semanal

### Google Calendar:
- Eventos automáticos para tarefas e missões com datas
- Lembretes configuráveis
- Categorização por cores
## 🔧 Solução de Problemas

### Problemas Comuns

**Código QR não aparece:**
- Certifique-se de estar executando em um terminal que suporta QR codes
- Tente executar com `DEBUG=true npm start` para logs detalhados

**Erros do Google Sheets:**
- Verifique se o arquivo de chave da conta de serviço existe e é um JSON válido
- Certifique-se de que a planilha está compartilhada com o email da conta de serviço
- Verifique se a API do Google Sheets está habilitada no projeto

**Erros do Gemini AI:**
- Verifique se sua chave de API está correta
- Verifique os limites de uso da API

**Erros do Google Calendar:**
- Certifique-se de que a API do Google Calendar está habilitada
- Verifique se a conta de serviço tem acesso ao calendário
- Confirme se o GOOGLE_CALENDAR_ID está correto

**Problemas de conexão WhatsApp:**
- Limpe a pasta `.wwebjs_auth` e reinicie
- Certifique-se de que o WhatsApp Web funciona no seu navegador primeiro

### 📋 Logs

Verifique o diretório de logs para informações detalhadas de erro:
```bash
tail -f logs/app.log
```

## 🤝 Contribuindo

1. Faça um fork do repositório
2. Crie uma branch para sua feature
3. Faça suas alterações
4. Adicione testes para novas funcionalidades
5. Execute os testes: `npm test`
6. Envie um pull request

## 📄 Licença

MIT

## 🎯 Roadmap

- [ ] Integração com Notion
- [ ] Suporte a anexos e imagens
- [ ] Dashboard web para análise
- [ ] Integração com Slack/Discord
- [ ] Comandos de voz
- [ ] Relatórios automáticos por email

## 💡 Suporte

Para suporte e dúvidas:
- Abra uma issue no GitHub
- Consulte a documentação completa
- Execute `node test-integrations.js` para diagnósticos

---

**Desenvolvido com ❤️ para produtividade em português brasileiro**
