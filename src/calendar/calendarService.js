const { google } = require('googleapis');
const { Logger } = require('../utils/logger.js');
const fs = require('fs');

class CalendarService {
    constructor() {
        this.calendar = null;
        this.logger = new Logger();
        this.calendarId = process.env.GOOGLE_CALENDAR_ID || 'primary';
    }

    async initialize() {
        try {
            let auth;

            // Try to load from file first
            const keyPath = process.env.GOOGLE_SERVICE_ACCOUNT_KEY_PATH;
            if (keyPath && fs.existsSync(keyPath)) {
                const credentials = JSON.parse(fs.readFileSync(keyPath, 'utf8'));
                auth = new google.auth.JWT(
                    credentials.client_email,
                    null,
                    credentials.private_key,
                    ['https://www.googleapis.com/auth/calendar']
                );
            }
            // If file doesn't exist, try to load from environment variable
            else if (process.env.GOOGLE_SERVICE_ACCOUNT_KEY) {
                const credentials = JSON.parse(process.env.GOOGLE_SERVICE_ACCOUNT_KEY);
                auth = new google.auth.JWT(
                    credentials.client_email,
                    null,
                    credentials.private_key,
                    ['https://www.googleapis.com/auth/calendar']
                );
            }
            // If neither exists, use Application Default Credentials (for Cloud Run)
            else {
                this.logger.info('Using Application Default Credentials for Google Calendar API');
                auth = new google.auth.GoogleAuth({
                    scopes: ['https://www.googleapis.com/auth/calendar']
                });
            }

            // Authorize the client (only needed for JWT)
            if (auth.authorize) {
                await auth.authorize();
            }

            // Initialize Calendar API
            this.calendar = google.calendar({ version: 'v3', auth });

            this.logger.info('Google Calendar service initialized successfully');
            return true;
        } catch (error) {
            this.logger.error('Failed to initialize Google Calendar service:', error);
            throw error;
        }
    }

    /**
     * Create a calendar event for a todo or quest
     * @param {Object} eventData - Event data
     * @param {string} eventData.title - Event title
     * @param {string} eventData.description - Event description
     * @param {Date} eventData.startDate - Start date and time
     * @param {Date} eventData.endDate - End date and time (optional)
     * @param {string} eventData.type - Event type ('todo' or 'quest')
     * @param {string} eventData.userPhone - User phone number
     * @returns {Promise<Object>} Created event
     */
    async createEvent(eventData) {
        try {
            const { title, description, startDate, endDate, type, userPhone } = eventData;

            // Calculate end time if not provided (default 1 hour for todos, all day for quests)
            let calculatedEndDate = endDate;
            if (!calculatedEndDate) {
                calculatedEndDate = new Date(startDate);
                if (type === 'todo') {
                    calculatedEndDate.setHours(calculatedEndDate.getHours() + 1);
                } else {
                    calculatedEndDate.setHours(23, 59, 59);
                }
            }

            const event = {
                summary: `${type === 'todo' ? '📝' : '🎯'} ${title}`,
                description: `${description}\n\n📱 Criado via WhatsApp Bot\n👤 Usuário: ${userPhone}\n🏷️ Tipo: ${type === 'todo' ? 'Tarefa' : 'Missão'}`,
                start: {
                    dateTime: startDate.toISOString(),
                    timeZone: process.env.DEFAULT_TIMEZONE || 'America/New_York',
                },
                end: {
                    dateTime: calculatedEndDate.toISOString(),
                    timeZone: process.env.DEFAULT_TIMEZONE || 'America/New_York',
                },
                colorId: type === 'todo' ? '1' : '9', // Blue for todos, Purple for quests
                reminders: {
                    useDefault: false,
                    overrides: [
                        { method: 'popup', minutes: 30 },
                        { method: 'email', minutes: 60 }
                    ]
                }
            };

            const response = await this.calendar.events.insert({
                calendarId: this.calendarId,
                resource: event,
            });

            this.logger.info(`Calendar event created: ${response.data.id} for ${type}: ${title}`);
            return {
                success: true,
                eventId: response.data.id,
                event: response.data
            };
        } catch (error) {
            this.logger.error('Error creating calendar event:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Update a calendar event
     * @param {string} eventId - Calendar event ID
     * @param {Object} updates - Updates to apply
     * @returns {Promise<Object>} Update result
     */
    async updateEvent(eventId, updates) {
        try {
            // First get the existing event
            const existingEvent = await this.calendar.events.get({
                calendarId: this.calendarId,
                eventId: eventId
            });

            // Merge updates with existing event
            const updatedEvent = {
                ...existingEvent.data,
                ...updates
            };

            const response = await this.calendar.events.update({
                calendarId: this.calendarId,
                eventId: eventId,
                resource: updatedEvent,
            });

            this.logger.info(`Calendar event updated: ${eventId}`);
            return {
                success: true,
                event: response.data
            };
        } catch (error) {
            this.logger.error('Error updating calendar event:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Delete a calendar event
     * @param {string} eventId - Calendar event ID
     * @returns {Promise<Object>} Delete result
     */
    async deleteEvent(eventId) {
        try {
            await this.calendar.events.delete({
                calendarId: this.calendarId,
                eventId: eventId
            });

            this.logger.info(`Calendar event deleted: ${eventId}`);
            return {
                success: true
            };
        } catch (error) {
            this.logger.error('Error deleting calendar event:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Get events for a specific date range
     * @param {Date} startDate - Start date
     * @param {Date} endDate - End date
     * @returns {Promise<Array>} List of events
     */
    async getEvents(startDate, endDate) {
        try {
            const response = await this.calendar.events.list({
                calendarId: this.calendarId,
                timeMin: startDate.toISOString(),
                timeMax: endDate.toISOString(),
                singleEvents: true,
                orderBy: 'startTime',
            });

            return {
                success: true,
                events: response.data.items || []
            };
        } catch (error) {
            this.logger.error('Error getting calendar events:', error);
            return {
                success: false,
                error: error.message,
                events: []
            };
        }
    }

    /**
     * Format date for calendar event
     * @param {Date} date - Date to format
     * @param {boolean} allDay - Whether this is an all-day event
     * @returns {Object} Formatted date object
     */
    formatDateForCalendar(date, allDay = false) {
        if (allDay) {
            return {
                date: date.toISOString().split('T')[0]
            };
        } else {
            return {
                dateTime: date.toISOString(),
                timeZone: process.env.DEFAULT_TIMEZONE || 'America/New_York'
            };
        }
    }

    /**
     * Parse date from Portuguese text and create appropriate calendar date
     * @param {string} dateText - Date text in Portuguese
     * @param {boolean} hasTime - Whether the date includes time
     * @returns {Date} Parsed date
     */
    parseDateForCalendar(dateText, hasTime = false) {
        // This would integrate with your existing date parser
        // For now, return a basic implementation
        const date = new Date(dateText);
        
        if (!hasTime) {
            // Set to 9 AM if no time specified
            date.setHours(9, 0, 0, 0);
        }
        
        return date;
    }
}


module.exports = { CalendarService };