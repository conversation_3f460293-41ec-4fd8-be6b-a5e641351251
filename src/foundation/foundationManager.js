import { Logger } from '../utils/logger.js';
import { Messages } from '../utils/messages.js';

export class FoundationManager {
    constructor(sheetsService) {
        this.sheetsService = sheetsService;
        this.logger = new Logger();
    }

    async addFoundation(userPhone, foundationData) {
        try {
            // Validate foundation data
            if (!foundationData.name || foundationData.name.trim() === '') {
                throw new Error('Foundation name is required');
            }

            // Check if foundation already exists for this user
            const existingFoundations = await this.getFoundations(userPhone);
            const duplicate = existingFoundations.find(f => 
                f.name.toLowerCase() === foundationData.name.toLowerCase()
            );

            if (duplicate) {
                throw new Error(`Foundation "${foundationData.name}" already exists`);
            }

            // Check foundation limit (max 5 foundations per user)
            if (existingFoundations.length >= 5) {
                throw new Error('Maximum of 5 foundations allowed per user');
            }

            const foundation = await this.sheetsService.addFoundation(userPhone, {
                name: foundationData.name.trim(),
                description: foundationData.description || '',
                targetScore: foundationData.targetScore || 10,
                currentScore: foundationData.currentScore || 0,
                status: 'active'
            });

            this.logger.info(`Foundation added for user ${userPhone}: ${foundation.name}`);
            return foundation;

        } catch (error) {
            this.logger.error('Error adding foundation:', error);
            throw error;
        }
    }

    async getFoundations(userPhone, filter = {}) {
        try {
            let foundations = await this.sheetsService.getFoundations(userPhone);

            // Apply filters
            if (filter.status) {
                foundations = foundations.filter(f => f.status === filter.status);
            }

            // Sort by creation date (newest first)
            foundations.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

            return foundations;

        } catch (error) {
            this.logger.error('Error getting foundations:', error);
            throw error;
        }
    }

    async updateFoundationScore(userPhone, foundationIdentifier, newScore) {
        try {
            const foundations = await this.getFoundations(userPhone);
            const foundation = this.findFoundation(foundations, foundationIdentifier);

            if (!foundation) {
                return { success: false, error: 'Foundation not found' };
            }

            // Validate score
            if (newScore < 0 || newScore > foundation.targetScore) {
                return { 
                    success: false, 
                    error: `Score must be between 0 and ${foundation.targetScore}` 
                };
            }

            // Update foundation score in sheets
            await this.sheetsService.updateFoundation(foundation.id, {
                currentScore: newScore
            });

            foundation.currentScore = newScore;
            
            this.logger.info(`Foundation score updated for user ${userPhone}: ${foundation.name} = ${newScore}`);
            return { success: true, foundation };

        } catch (error) {
            this.logger.error('Error updating foundation score:', error);
            return { success: false, error: error.message };
        }
    }

    async getFoundationStats(userPhone) {
        try {
            const foundations = await this.getFoundations(userPhone);
            
            const stats = {
                total: foundations.length,
                active: foundations.filter(f => f.status === 'active').length,
                averageScore: 0,
                totalProgress: 0,
                foundationDetails: []
            };

            if (foundations.length > 0) {
                const totalCurrentScore = foundations.reduce((sum, f) => sum + f.currentScore, 0);
                const totalTargetScore = foundations.reduce((sum, f) => sum + f.targetScore, 0);
                
                stats.averageScore = Math.round((totalCurrentScore / foundations.length) * 10) / 10;
                stats.totalProgress = totalTargetScore > 0 ? 
                    Math.round((totalCurrentScore / totalTargetScore) * 100) : 0;

                stats.foundationDetails = foundations.map(f => ({
                    name: f.name,
                    currentScore: f.currentScore,
                    targetScore: f.targetScore,
                    progress: Math.round((f.currentScore / f.targetScore) * 100),
                    status: f.status
                }));
            }

            return stats;

        } catch (error) {
            this.logger.error('Error getting foundation stats:', error);
            throw error;
        }
    }

    findFoundation(foundations, identifier) {
        // Try to find by ID first
        let foundation = foundations.find(f => f.id === identifier);
        
        if (!foundation) {
            // Try to find by name (case insensitive)
            foundation = foundations.find(f => 
                f.name.toLowerCase().includes(identifier.toLowerCase())
            );
        }

        if (!foundation) {
            // Try to find by index (1-based)
            const index = parseInt(identifier);
            if (!isNaN(index) && index > 0 && index <= foundations.length) {
                foundation = foundations[index - 1];
            }
        }

        return foundation;
    }

    formatFoundationsList(foundations) {
        if (foundations.length === 0) {
            return Messages.FOUNDATIONS.NO_FOUNDATIONS;
        }

        let response = Messages.FOUNDATIONS.LIST_HEADER;
        
        foundations.forEach((foundation, index) => {
            const progress = Math.round((foundation.currentScore / foundation.targetScore) * 100);
            const progressBar = this.generateProgressBar(progress);
            
            response += `${index + 1}. **${foundation.name}**\n`;
            response += `   📊 ${foundation.currentScore}/${foundation.targetScore} (${progress}%)\n`;
            response += `   ${progressBar}\n`;
            if (foundation.description) {
                response += `   📝 ${foundation.description}\n`;
            }
            response += '\n';
        });

        return response;
    }

    generateProgressBar(percentage, length = 10) {
        const filled = Math.round((percentage / 100) * length);
        const empty = length - filled;
        return '█'.repeat(filled) + '░'.repeat(empty);
    }
}
