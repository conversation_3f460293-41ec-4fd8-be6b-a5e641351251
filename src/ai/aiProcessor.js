const { GoogleGenerativeAI } = require('@google/generative-ai');
const { DateParser } = require('../utils/dateParser.js');
const { Logger } = require('../utils/logger.js');
const { Messages } = require('../utils/messages.js');

class AIProcessor {
    constructor() {
        this.genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
        this.model = this.genAI.getGenerativeModel({ model: "gemini-1.5-flash" });
        this.dateParser = new DateParser();
        this.logger = new Logger();
    }

    async processMessage(messageText) {
        try {
            const prompt = this.buildPrompt(messageText);
            
            const fullPrompt = `${prompt.system}\n\nMensagem do usuário: "${messageText}"`;

            const result = await this.model.generateContent(fullPrompt);
            const response = await result.response;
            const aiResponse = response.text().trim();
            return this.parseAIResponse(aiResponse, messageText);

        } catch (error) {
            this.logger.error('Error processing message with AI:', error);
            // Fallback to rule-based processing
            return this.fallbackProcessing(messageText);
        }
    }

    buildPrompt(messageText) {
        const system = `Você é um assistente de produtividade. Analise mensagens do usuário e extraia a intenção e informações relevantes.

IMPORTANTE: Sempre tente correlacionar missões (todos) com fundamentos e objetivos (quests) quando possível. Extraia essas correlações das mensagens do usuário.

Responda com um objeto JSON contendo:
- action: um de [add_todo, list_todos, complete_todo, delete_todo, update_todo, add_foundation, list_foundations, update_foundation_score, add_quest, list_quests, update_quest_progress, help, stats, unknown]
- todoData: (para add_todo) objeto com title, property, dueDate (formato YYYY-MM-DD ou null), obs, foundation, quest, books, url, study, priority (high/medium/low)
- todoIdentifier: (para complete/delete/update) string para identificar qual tarefa
- filter: (para list_todos) objeto com completed (true/false/null), overdue (true/false), today (true/false)
- updates: (para update_todo) objeto com campos para atualizar
- foundationData: (para add_foundation) objeto com name, date, relatedActions, relatedMission, relatedPRDs, relatedProblems, relatedRoadmap, status, text, totalProgress, keyResults
- foundationIdentifier: (para update_foundation_score) string para identificar qual fundamento
- score: (para update_foundation_score) número da nova pontuação
- questData: (para add_quest) objeto com title, foundation, how, target90d, ytd90d, url, dueDate, status, mission, projects, howWasIt, initialValue, progress, review, done
- questIdentifier: (para update_quest_progress) string para identificar qual missão
- progress: (para update_quest_progress) número da porcentagem de progresso (0-100)

Exemplos em português:
"adicionar comprar mantimentos amanhã para fundamento saúde" -> {"action": "add_todo", "todoData": {"title": "comprar mantimentos", "dueDate": "2024-01-15", "priority": "medium", "foundation": "saúde"}}
"listar minhas tarefas" -> {"action": "list_todos", "filter": {}}
"marcar mantimentos como feito" -> {"action": "complete_todo", "todoIdentifier": "mantimentos"}
"excluir tarefa da reunião" -> {"action": "delete_todo", "todoIdentifier": "reunião"}
"ajuda" -> {"action": "help"}
"estatísticas" -> {"action": "stats"}

Palavras-chave em português:
- Adicionar: adicionar, criar, nova, novo, lembrar, tarefa, fazer
- Listar: listar, mostrar, exibir, tarefas, lista, ver, quais
- Concluir: concluir, feito, finalizar, marcar, completar, terminar
- Excluir: excluir, remover, cancelar, apagar, deletar
- Ajuda: ajuda, como, comandos, socorro
- Estatísticas: estatísticas, resumo, números, dados
- Fundamentos: fundamento, fundamentos, base, bases, pilar, pilares
- Missões: missão, missões, quest, quests, objetivo, objetivos, meta, metas
- Progresso: progresso, atualizar, pontuação, score, porcentagem
- Prioridades: urgente/importante (high), baixa prioridade (low)
- Filtros: atrasadas/vencidas (overdue), hoje (today), concluídas (completed), pendentes (pending)`;

        const user = `Analise esta mensagem: "${messageText}"`;

        return { system, user };
    }

    parseAIResponse(aiResponse, originalMessage) {
        try {
            // Remove markdown code blocks if present
            const cleanResponse = aiResponse.replace(/```json\n?|\n?```/g, '').trim();
            const parsed = JSON.parse(cleanResponse);

            // Process dates if present
            if (parsed.todoData && parsed.todoData.dueDate) {
                parsed.todoData.dueDate = this.dateParser.parseDate(parsed.todoData.dueDate);
            }

            return parsed;
        } catch (error) {
            this.logger.error('Error parsing AI response:', error);
            return this.fallbackProcessing(originalMessage);
        }
    }

    fallbackProcessing(messageText) {
        const text = messageText.toLowerCase().trim();

        // Simple rule-based fallback
        if (this.isAddTodoMessage(text)) {
            return this.extractTodoFromMessage(text);
        } else if (this.isListTodosMessage(text)) {
            return { action: 'list_todos', filter: this.extractListFilter(text) };
        } else if (this.isCompleteTodoMessage(text)) {
            return { action: 'complete_todo', todoIdentifier: this.extractTodoIdentifier(text) };
        } else if (this.isDeleteTodoMessage(text)) {
            return { action: 'delete_todo', todoIdentifier: this.extractTodoIdentifier(text) };
        } else if (this.isAddFoundationMessage(text)) {
            return this.extractFoundationFromMessage(text);
        } else if (this.isListFoundationsMessage(text)) {
            return { action: 'list_foundations' };
        } else if (this.isUpdateFoundationScoreMessage(text)) {
            return this.extractFoundationScoreUpdate(text);
        } else if (this.isAddQuestMessage(text)) {
            return this.extractQuestFromMessage(text);
        } else if (this.isListQuestsMessage(text)) {
            return { action: 'list_quests' };
        } else if (this.isUpdateQuestProgressMessage(text)) {
            return this.extractQuestProgressUpdate(text);
        } else if (this.isHelpMessage(text)) {
            return { action: 'help' };
        } else if (this.isStatsMessage(text)) {
            return { action: 'stats' };
        } else {
            return { action: 'unknown' };
        }
    }

    isAddTodoMessage(text) {
        return Messages.KEYWORDS.ADD.some(keyword => text.includes(keyword));
    }

    isListTodosMessage(text) {
        return Messages.KEYWORDS.LIST.some(keyword => text.includes(keyword));
    }

    isCompleteTodoMessage(text) {
        return Messages.KEYWORDS.COMPLETE.some(keyword => text.includes(keyword));
    }

    isDeleteTodoMessage(text) {
        return Messages.KEYWORDS.DELETE.some(keyword => text.includes(keyword));
    }

    isHelpMessage(text) {
        return Messages.KEYWORDS.HELP.some(keyword => text.includes(keyword));
    }

    isStatsMessage(text) {
        return Messages.KEYWORDS.STATS.some(keyword => text.includes(keyword));
    }

    extractTodoFromMessage(text) {
        // Remove common prefixes in Portuguese
        let title = text
            .replace(/^(adicionar|criar|nova?|lembrar|tarefa|fazer)\s*/i, '')
            .replace(/^(me lembrar de|lembrar de)\s*/i, '')
            .replace(/^(de\s+)?/, '');

        // Extract date information
        const dueDate = this.dateParser.extractDateFromText(title);

        // Remove date text from title
        if (dueDate.dateText) {
            title = title.replace(dueDate.dateText, '').trim();
        }

        // Determine priority
        const priority = this.extractPriority(text);

        return {
            action: 'add_todo',
            todoData: {
                title: title || 'Nova tarefa',
                dueDate: dueDate.date,
                priority: priority
            }
        };
    }

    extractTodoIdentifier(text) {
        // Simple extraction - look for quoted text or key phrases
        const quotedMatch = text.match(/"([^"]+)"/);
        if (quotedMatch) {
            return quotedMatch[1];
        }

        // Look for common patterns in Portuguese
        const patterns = [
            /(?:tarefa|task)\s+(?:sobre|para|chamada?)\s+(.+)/i,
            /(?:a\s+)?(.+?)\s+(?:tarefa|task)/i,
            /(?:marcar|concluir|excluir|remover|deletar)\s+(.+?)(?:\s+como\s+feito|\s+tarefa)?$/i,
            /(?:feito|concluído)\s+com\s+(.+)/i
        ];

        for (const pattern of patterns) {
            const match = text.match(pattern);
            if (match) {
                return match[1].trim();
            }
        }

        // Fallback: return the text after common action words in Portuguese
        return text.replace(/^(marcar|concluir|excluir|remover|deletar|feito|finalizar)\s+/i, '').trim();
    }

    extractListFilter(text) {
        const filter = {};

        if (Messages.KEYWORDS.FILTERS.OVERDUE.some(keyword => text.includes(keyword))) {
            filter.overdue = true;
        }
        if (Messages.KEYWORDS.FILTERS.TODAY.some(keyword => text.includes(keyword))) {
            filter.today = true;
        }
        if (Messages.KEYWORDS.FILTERS.COMPLETED.some(keyword => text.includes(keyword))) {
            filter.completed = true;
        }
        if (Messages.KEYWORDS.FILTERS.PENDING.some(keyword => text.includes(keyword))) {
            filter.completed = false;
        }

        return filter;
    }

    extractPriority(text) {
        if (Messages.KEYWORDS.PRIORITY.HIGH.some(keyword => text.includes(keyword))) {
            return 'high';
        }
        if (Messages.KEYWORDS.PRIORITY.LOW.some(keyword => text.includes(keyword))) {
            return 'low';
        }
        return 'medium';
    }

    // Foundation detection methods
    isAddFoundationMessage(text) {
        const addKeywords = Messages.KEYWORDS.ADD;
        const foundationKeywords = Messages.KEYWORDS.FOUNDATIONS;

        return addKeywords.some(keyword => text.includes(keyword)) &&
               foundationKeywords.some(keyword => text.includes(keyword));
    }

    isListFoundationsMessage(text) {
        const listKeywords = Messages.KEYWORDS.LIST;
        const foundationKeywords = Messages.KEYWORDS.FOUNDATIONS;

        return listKeywords.some(keyword => text.includes(keyword)) &&
               foundationKeywords.some(keyword => text.includes(keyword));
    }

    isUpdateFoundationScoreMessage(text) {
        const foundationKeywords = Messages.KEYWORDS.FOUNDATIONS;
        const progressKeywords = ['progresso', 'pontuação', 'score', 'atualizar', 'nota'];

        return foundationKeywords.some(keyword => text.includes(keyword)) &&
               progressKeywords.some(keyword => text.includes(keyword));
    }

    extractFoundationFromMessage(text) {
        // Remove common prefixes
        let name = text
            .replace(/^(adicionar|criar|novo?)\s*(fundamento|base|pilar)\s*/i, '')
            .replace(/^(fundamento|base|pilar)\s*/i, '')
            .trim();

        return {
            action: 'add_foundation',
            foundationData: {
                name: name || 'Novo Fundamento',
                description: '',
                targetScore: 10
            }
        };
    }

    extractFoundationScoreUpdate(text) {
        // Extract foundation identifier and score
        const scoreMatch = text.match(/(\d+)/);
        const score = scoreMatch ? parseInt(scoreMatch[1]) : 0;

        // Remove score and keywords to get foundation name
        let identifier = text
            .replace(/\d+/g, '')
            .replace(/(progresso|pontuação|score|atualizar|nota|fundamento|base|pilar)/gi, '')
            .trim();

        return {
            action: 'update_foundation_score',
            foundationIdentifier: identifier || '1',
            score: score
        };
    }

    // Quest detection methods
    isAddQuestMessage(text) {
        const addKeywords = Messages.KEYWORDS.ADD;
        const questKeywords = Messages.KEYWORDS.QUESTS;

        return addKeywords.some(keyword => text.includes(keyword)) &&
               questKeywords.some(keyword => text.includes(keyword));
    }

    isListQuestsMessage(text) {
        const listKeywords = Messages.KEYWORDS.LIST;
        const questKeywords = Messages.KEYWORDS.QUESTS;

        return listKeywords.some(keyword => text.includes(keyword)) &&
               questKeywords.some(keyword => text.includes(keyword));
    }

    isUpdateQuestProgressMessage(text) {
        const questKeywords = Messages.KEYWORDS.QUESTS;
        const progressKeywords = ['progresso', 'porcentagem', '%', 'atualizar', 'completar'];

        return questKeywords.some(keyword => text.includes(keyword)) &&
               progressKeywords.some(keyword => text.includes(keyword));
    }

    extractQuestFromMessage(text) {
        // Remove common prefixes
        let title = text
            .replace(/^(adicionar|criar|nova?)\s*(missão|quest|objetivo|meta)\s*/i, '')
            .replace(/^(missão|quest|objetivo|meta)\s*/i, '')
            .trim();

        // Extract date information
        const dueDate = this.dateParser.extractDateFromText(title);

        // Remove date text from title
        if (dueDate.dateText) {
            title = title.replace(dueDate.dateText, '').trim();
        }

        return {
            action: 'add_quest',
            questData: {
                title: title || 'Nova Missão',
                description: '',
                dueDate: dueDate.date,
                weeklyGoal: '',
                measurementMetric: ''
            }
        };
    }

    extractQuestProgressUpdate(text) {
        // Extract progress percentage
        const progressMatch = text.match(/(\d+)%?/);
        const progress = progressMatch ? parseInt(progressMatch[1]) : 0;

        // Remove progress and keywords to get quest identifier
        let identifier = text
            .replace(/\d+%?/g, '')
            .replace(/(progresso|porcentagem|atualizar|completar|missão|quest|objetivo|meta)/gi, '')
            .trim();

        return {
            action: 'update_quest_progress',
            questIdentifier: identifier || '1',
            progress: progress
        };
    }
}


module.exports = { AIProcessor };