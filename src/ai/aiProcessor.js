import OpenAI from 'openai';
import { DateParser } from '../utils/dateParser.js';
import { Logger } from '../utils/logger.js';
import { Messages } from '../utils/messages.js';

export class AIProcessor {
    constructor() {
        this.openai = new OpenAI({
            apiKey: process.env.OPENAI_API_KEY
        });
        this.dateParser = new DateParser();
        this.logger = new Logger();
    }

    async processMessage(messageText) {
        try {
            const prompt = this.buildPrompt(messageText);
            
            const response = await this.openai.chat.completions.create({
                model: "gpt-3.5-turbo",
                messages: [
                    {
                        role: "system",
                        content: prompt.system
                    },
                    {
                        role: "user",
                        content: prompt.user
                    }
                ],
                temperature: 0.1,
                max_tokens: 500
            });

            const aiResponse = response.choices[0].message.content;
            return this.parseAIResponse(aiResponse, messageText);

        } catch (error) {
            this.logger.error('Error processing message with AI:', error);
            // Fallback to rule-based processing
            return this.fallbackProcessing(messageText);
        }
    }

    buildPrompt(messageText) {
        const system = `Você é um assistente de lista de tarefas. Analise mensagens do usuário e extraia a intenção e informações relevantes.

Responda com um objeto JSON contendo:
- action: um de [add_todo, list_todos, complete_todo, delete_todo, update_todo, help, stats, unknown]
- todoData: (para add_todo) objeto com title, dueDate (formato YYYY-MM-DD ou null), priority (high/medium/low)
- todoIdentifier: (para complete/delete/update) string para identificar qual tarefa
- filter: (para list_todos) objeto com completed (true/false/null), overdue (true/false), today (true/false)
- updates: (para update_todo) objeto com campos para atualizar

Exemplos em português:
"adicionar comprar mantimentos amanhã" -> {"action": "add_todo", "todoData": {"title": "comprar mantimentos", "dueDate": "2024-01-15", "priority": "medium"}}
"listar minhas tarefas" -> {"action": "list_todos", "filter": {}}
"marcar mantimentos como feito" -> {"action": "complete_todo", "todoIdentifier": "mantimentos"}
"excluir tarefa da reunião" -> {"action": "delete_todo", "todoIdentifier": "reunião"}
"ajuda" -> {"action": "help"}
"estatísticas" -> {"action": "stats"}

Palavras-chave em português:
- Adicionar: adicionar, criar, nova, novo, lembrar, tarefa, fazer
- Listar: listar, mostrar, exibir, tarefas, lista, ver, quais
- Concluir: concluir, feito, finalizar, marcar, completar, terminar
- Excluir: excluir, remover, cancelar, apagar, deletar
- Ajuda: ajuda, como, comandos, socorro
- Estatísticas: estatísticas, resumo, números, dados
- Prioridades: urgente/importante (high), baixa prioridade (low)
- Filtros: atrasadas/vencidas (overdue), hoje (today), concluídas (completed), pendentes (pending)`;

        const user = `Analise esta mensagem: "${messageText}"`;

        return { system, user };
    }

    parseAIResponse(aiResponse, originalMessage) {
        try {
            const parsed = JSON.parse(aiResponse);
            
            // Process dates if present
            if (parsed.todoData && parsed.todoData.dueDate) {
                parsed.todoData.dueDate = this.dateParser.parseDate(parsed.todoData.dueDate);
            }

            return parsed;
        } catch (error) {
            this.logger.error('Error parsing AI response:', error);
            return this.fallbackProcessing(originalMessage);
        }
    }

    fallbackProcessing(messageText) {
        const text = messageText.toLowerCase().trim();

        // Simple rule-based fallback
        if (this.isAddTodoMessage(text)) {
            return this.extractTodoFromMessage(text);
        } else if (this.isListTodosMessage(text)) {
            return { action: 'list_todos', filter: this.extractListFilter(text) };
        } else if (this.isCompleteTodoMessage(text)) {
            return { action: 'complete_todo', todoIdentifier: this.extractTodoIdentifier(text) };
        } else if (this.isDeleteTodoMessage(text)) {
            return { action: 'delete_todo', todoIdentifier: this.extractTodoIdentifier(text) };
        } else if (this.isHelpMessage(text)) {
            return { action: 'help' };
        } else if (this.isStatsMessage(text)) {
            return { action: 'stats' };
        } else {
            return { action: 'unknown' };
        }
    }

    isAddTodoMessage(text) {
        return Messages.KEYWORDS.ADD.some(keyword => text.includes(keyword));
    }

    isListTodosMessage(text) {
        return Messages.KEYWORDS.LIST.some(keyword => text.includes(keyword));
    }

    isCompleteTodoMessage(text) {
        return Messages.KEYWORDS.COMPLETE.some(keyword => text.includes(keyword));
    }

    isDeleteTodoMessage(text) {
        return Messages.KEYWORDS.DELETE.some(keyword => text.includes(keyword));
    }

    isHelpMessage(text) {
        return Messages.KEYWORDS.HELP.some(keyword => text.includes(keyword));
    }

    isStatsMessage(text) {
        return Messages.KEYWORDS.STATS.some(keyword => text.includes(keyword));
    }

    extractTodoFromMessage(text) {
        // Remove common prefixes in Portuguese
        let title = text
            .replace(/^(adicionar|criar|nova?|lembrar|tarefa|fazer)\s*/i, '')
            .replace(/^(me lembrar de|lembrar de)\s*/i, '')
            .replace(/^(de\s+)?/, '');

        // Extract date information
        const dueDate = this.dateParser.extractDateFromText(title);

        // Remove date text from title
        if (dueDate.dateText) {
            title = title.replace(dueDate.dateText, '').trim();
        }

        // Determine priority
        const priority = this.extractPriority(text);

        return {
            action: 'add_todo',
            todoData: {
                title: title || 'Nova tarefa',
                dueDate: dueDate.date,
                priority: priority
            }
        };
    }

    extractTodoIdentifier(text) {
        // Simple extraction - look for quoted text or key phrases
        const quotedMatch = text.match(/"([^"]+)"/);
        if (quotedMatch) {
            return quotedMatch[1];
        }

        // Look for common patterns in Portuguese
        const patterns = [
            /(?:tarefa|task)\s+(?:sobre|para|chamada?)\s+(.+)/i,
            /(?:a\s+)?(.+?)\s+(?:tarefa|task)/i,
            /(?:marcar|concluir|excluir|remover|deletar)\s+(.+?)(?:\s+como\s+feito|\s+tarefa)?$/i,
            /(?:feito|concluído)\s+com\s+(.+)/i
        ];

        for (const pattern of patterns) {
            const match = text.match(pattern);
            if (match) {
                return match[1].trim();
            }
        }

        // Fallback: return the text after common action words in Portuguese
        return text.replace(/^(marcar|concluir|excluir|remover|deletar|feito|finalizar)\s+/i, '').trim();
    }

    extractListFilter(text) {
        const filter = {};

        if (Messages.KEYWORDS.FILTERS.OVERDUE.some(keyword => text.includes(keyword))) {
            filter.overdue = true;
        }
        if (Messages.KEYWORDS.FILTERS.TODAY.some(keyword => text.includes(keyword))) {
            filter.today = true;
        }
        if (Messages.KEYWORDS.FILTERS.COMPLETED.some(keyword => text.includes(keyword))) {
            filter.completed = true;
        }
        if (Messages.KEYWORDS.FILTERS.PENDING.some(keyword => text.includes(keyword))) {
            filter.completed = false;
        }

        return filter;
    }

    extractPriority(text) {
        if (Messages.KEYWORDS.PRIORITY.HIGH.some(keyword => text.includes(keyword))) {
            return 'high';
        }
        if (Messages.KEYWORDS.PRIORITY.LOW.some(keyword => text.includes(keyword))) {
            return 'low';
        }
        return 'medium';
    }
}
