import dotenv from 'dotenv';
import { WhatsAppBot } from './bot/whatsappBot.js';
import { TodoManager } from './todo/todoManager.js';
import { SheetsService } from './sheets/sheetsService.js';
import { AIProcessor } from './ai/aiProcessor.js';
import { FoundationManager } from './foundation/foundationManager.js';
import { QuestManager } from './quest/questManager.js';
import { CalendarService } from './calendar/calendarService.js';
import { Logger } from './utils/logger.js';

// Load environment variables
dotenv.config();

class TodoBotApp {
    constructor() {
        this.logger = new Logger();
        this.sheetsService = null;
        this.calendarService = null;
        this.aiProcessor = null;
        this.todoManager = null;
        this.foundationManager = null;
        this.questManager = null;
        this.whatsappBot = null;
    }

    async initialize() {
        try {
            this.logger.info('Initializing WhatsApp Todo Bot...');

            // Initialize Google Sheets service
            this.sheetsService = new SheetsService();
            await this.sheetsService.initialize();
            this.logger.info('Google Sheets service initialized');

            // Initialize Google Calendar service
            try {
                this.calendarService = new CalendarService();
                await this.calendarService.initialize();
                this.logger.info('Google Calendar service initialized');
            } catch (error) {
                this.logger.warn('Google Calendar service initialization failed, continuing without calendar sync:', error.message);
                this.calendarService = null;
            }

            // Initialize AI processor
            this.aiProcessor = new AIProcessor();
            this.logger.info('AI processor initialized');

            // Initialize Todo manager
            this.todoManager = new TodoManager(this.sheetsService, this.calendarService);
            this.logger.info('Todo manager initialized');

            // Initialize Foundation manager
            this.foundationManager = new FoundationManager(this.sheetsService);
            this.logger.info('Foundation manager initialized');

            // Initialize Quest manager
            this.questManager = new QuestManager(this.sheetsService, this.calendarService);
            this.logger.info('Quest manager initialized');

            // Initialize WhatsApp bot with all managers
            this.whatsappBot = new WhatsAppBot(
                this.todoManager,
                this.aiProcessor,
                this.sheetsService,
                this.foundationManager,
                this.questManager
            );
            await this.whatsappBot.initialize();
            this.logger.info('WhatsApp bot initialized');

            this.logger.info('🚀 WhatsApp Todo Bot is ready!');
        } catch (error) {
            this.logger.error('Failed to initialize bot:', error);
            process.exit(1);
        }
    }

    async shutdown() {
        this.logger.info('Shutting down bot...');
        if (this.whatsappBot) {
            await this.whatsappBot.destroy();
        }
        process.exit(0);
    }
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
    console.log('\nReceived SIGINT, shutting down gracefully...');
    if (global.todoBot) {
        await global.todoBot.shutdown();
    }
});

process.on('SIGTERM', async () => {
    console.log('\nReceived SIGTERM, shutting down gracefully...');
    if (global.todoBot) {
        await global.todoBot.shutdown();
    }
});

// Keep alive function for cloud hosting
function keepAlive() {
    // Ping every 25 minutes to prevent sleeping (for Render.com)
    setInterval(() => {
        console.log('🏓 Keep alive ping - Bot is running 24/7');
    }, 25 * 60 * 1000);
}

// Health check endpoint for cloud hosting
function setupHealthCheck() {
    const http = require('http');
    const server = http.createServer((req, res) => {
        if (req.url === '/health') {
            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({
                status: 'healthy',
                uptime: process.uptime(),
                timestamp: new Date().toISOString(),
                botRunning: global.todoBot ? true : false
            }));
        } else {
            res.writeHead(404);
            res.end('Not Found');
        }
    });

    const port = process.env.PORT || 8080; // Cloud Run usa 8080 por padrão
    server.listen(port, '0.0.0.0', () => {
        console.log(`🌐 Health check server running on port ${port}`);
        console.log(`🔗 Health check: http://0.0.0.0:${port}/health`);
        console.log(`☁️  Optimized for Google Cloud Run`);
    });
}

// Start the application
async function main() {
    try {
        console.log('🎯 Starting Foco Criador Bot for 24/7 operation...');

        // Setup cloud hosting features
        setupHealthCheck();
        keepAlive();

        global.todoBot = new TodoBotApp();
        await global.todoBot.initialize();

        console.log('🎉 Foco Criador is running 24/7!');
        console.log('📱 Send a message to your WhatsApp to test the bot');
        console.log('🌐 Bot accessible from anywhere with internet');
        console.log('☁️  Ready for Google Cloud Run deployment');

    } catch (error) {
        console.error('❌ Failed to start application:', error);
        process.exit(1);
    }
}

main().catch((error) => {
    console.error('Failed to start application:', error);
    process.exit(1);
});
