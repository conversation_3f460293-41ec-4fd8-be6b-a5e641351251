import dotenv from 'dotenv';
import http from 'http';
import QRCode from 'qrcode';
import { WhatsAppBot } from './bot/whatsappBot.js';
import { TodoManager } from './todo/todoManager.js';
import { SheetsService } from './sheets/sheetsService.js';
import { AIProcessor } from './ai/aiProcessor.js';
import { FoundationManager } from './foundation/foundationManager.js';
import { QuestManager } from './quest/questManager.js';
import { CalendarService } from './calendar/calendarService.js';
import { Logger } from './utils/logger.js';

// Load environment variables
dotenv.config();

class TodoBotApp {
    constructor() {
        this.logger = new Logger();
        this.sheetsService = null;
        this.calendarService = null;
        this.aiProcessor = null;
        this.todoManager = null;
        this.foundationManager = null;
        this.questManager = null;
        this.whatsappBot = null;
    }

    async initialize() {
        try {
            this.logger.info('Initializing WhatsApp Todo Bot...');

            // Initialize Google Sheets service
            this.sheetsService = new SheetsService();
            await this.sheetsService.initialize();
            this.logger.info('Google Sheets service initialized');

            // Initialize Google Calendar service
            try {
                this.calendarService = new CalendarService();
                await this.calendarService.initialize();
                this.logger.info('Google Calendar service initialized');
            } catch (error) {
                this.logger.warn('Google Calendar service initialization failed, continuing without calendar sync:', error.message);
                this.calendarService = null;
            }

            // Initialize AI processor
            this.aiProcessor = new AIProcessor();
            this.logger.info('AI processor initialized');

            // Initialize Todo manager
            this.todoManager = new TodoManager(this.sheetsService, this.calendarService);
            this.logger.info('Todo manager initialized');

            // Initialize Foundation manager
            this.foundationManager = new FoundationManager(this.sheetsService);
            this.logger.info('Foundation manager initialized');

            // Initialize Quest manager
            this.questManager = new QuestManager(this.sheetsService, this.calendarService);
            this.logger.info('Quest manager initialized');

            // Initialize WhatsApp bot with all managers
            this.whatsappBot = new WhatsAppBot(
                this.todoManager,
                this.aiProcessor,
                this.sheetsService,
                this.foundationManager,
                this.questManager
            );
            await this.whatsappBot.initialize();
            this.logger.info('WhatsApp bot initialized');

            this.logger.info('🚀 WhatsApp Todo Bot is ready!');
        } catch (error) {
            this.logger.error('Failed to initialize bot:', error);
            process.exit(1);
        }
    }

    async shutdown() {
        this.logger.info('Shutting down bot...');
        if (this.whatsappBot) {
            await this.whatsappBot.destroy();
        }
        process.exit(0);
    }
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
    console.log('\nReceived SIGINT, shutting down gracefully...');
    if (global.todoBot) {
        await global.todoBot.shutdown();
    }
});

process.on('SIGTERM', async () => {
    console.log('\nReceived SIGTERM, shutting down gracefully...');
    if (global.todoBot) {
        await global.todoBot.shutdown();
    }
});

// Keep alive function for cloud hosting
function keepAlive() {
    // Ping every 25 minutes to prevent sleeping (for Render.com)
    setInterval(() => {
        console.log('🏓 Keep alive ping - Bot is running 24/7');
    }, 25 * 60 * 1000);
}

// Health check endpoint for cloud hosting
function setupHealthCheck() {
    const server = http.createServer(async (req, res) => {
        if (req.url === '/health') {
            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({
                status: 'healthy',
                uptime: process.uptime(),
                timestamp: new Date().toISOString(),
                botRunning: global.todoBot ? true : false
            }));
        } else if (req.url === '/qr') {
            try {
                console.log('🔄 Forcing fresh WhatsApp authentication...');

                // Stop current bot connection first
                if (global.todoBot && global.todoBot.whatsappBot) {
                    try {
                        await global.todoBot.whatsappBot.destroy();
                        console.log('🛑 Stopped existing WhatsApp connection');
                    } catch (stopError) {
                        console.log('⚠️ Error stopping bot (continuing):', stopError.message);
                    }
                }

                // Clear any existing auth state more thoroughly
                const fs = await import('fs');
                const path = await import('path');
                const authPath = path.join(process.cwd(), 'baileys_auth');

                try {
                    if (fs.existsSync(authPath)) {
                        fs.rmSync(authPath, { recursive: true, force: true });
                        console.log('🗑️ Cleared existing auth state');
                    }

                    // Wait for filesystem operations to complete
                    await new Promise(resolve => setTimeout(resolve, 2000));
                } catch (fsError) {
                    console.log('⚠️ Error clearing auth (continuing):', fsError.message);
                }

                // Reinitialize bot to trigger fresh QR
                if (global.todoBot && global.todoBot.whatsappBot) {
                    console.log('🚀 Reinitializing WhatsApp bot for fresh QR...');
                    await global.todoBot.whatsappBot.initialize();
                }

                res.writeHead(200, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({
                    status: 'qr_requested',
                    message: '📱 QR code requested! Visit /qr-image to scan with WhatsApp',
                    qr_url: 'https://fococriador-6a2gqq6k7a-uc.a.run.app/qr-image',
                    timestamp: new Date().toISOString()
                }));
            } catch (error) {
                console.error('❌ Error generating QR:', error);
                res.writeHead(500, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({
                    status: 'error',
                    message: 'Failed to generate QR code',
                    error: error.message
                }));
            }
        } else if (req.url === '/qr-image') {
            try {
                // Get current QR code from WhatsApp bot
                const qrData = global.todoBot?.whatsappBot?.getCurrentQRCode();

                if (!qrData) {
                    // No QR code available, show instructions
                    res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
                    res.end(`
                        <!DOCTYPE html>
                        <html>
                        <head>
                            <title>Foco Criador - QR Code</title>
                            <meta charset="utf-8">
                            <meta name="viewport" content="width=device-width, initial-scale=1">
                            <style>
                                body { font-family: Arial, sans-serif; text-align: center; padding: 50px; background: #f5f5f5; }
                                .container { max-width: 500px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
                                .btn { background: #25D366; color: white; padding: 15px 30px; border: none; border-radius: 5px; font-size: 16px; cursor: pointer; text-decoration: none; display: inline-block; margin: 10px; }
                                .btn:hover { background: #128C7E; }
                            </style>
                        </head>
                        <body>
                            <div class="container">
                                <h1>🎯 Foco Criador</h1>
                                <h2>📱 WhatsApp QR Code</h2>
                                <p>Nenhum QR code disponível no momento.</p>
                                <p>Clique no botão abaixo para gerar um novo QR code:</p>
                                <a href="/qr" class="btn">🔄 Gerar QR Code</a>
                                <br><br>
                                <small>Após clicar, aguarde alguns segundos e atualize esta página.</small>
                            </div>
                        </body>
                        </html>
                    `);
                    return;
                }

                // Generate QR code image
                const qrImageBuffer = await QRCode.toBuffer(qrData, {
                    type: 'png',
                    width: 400,
                    margin: 2,
                    color: {
                        dark: '#000000',
                        light: '#FFFFFF'
                    }
                });

                // Return HTML page with QR code
                const qrImageBase64 = qrImageBuffer.toString('base64');
                res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
                res.end(`
                    <!DOCTYPE html>
                    <html>
                    <head>
                        <title>Foco Criador - QR Code</title>
                        <meta charset="utf-8">
                        <meta name="viewport" content="width=device-width, initial-scale=1">
                        <style>
                            body { font-family: Arial, sans-serif; text-align: center; padding: 20px; background: #f5f5f5; }
                            .container { max-width: 500px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
                            .qr-code { border: 2px solid #25D366; border-radius: 10px; padding: 20px; margin: 20px 0; background: white; }
                            .btn { background: #25D366; color: white; padding: 10px 20px; border: none; border-radius: 5px; font-size: 14px; cursor: pointer; text-decoration: none; display: inline-block; margin: 5px; }
                            .btn:hover { background: #128C7E; }
                            .instructions { background: #e8f5e8; padding: 15px; border-radius: 5px; margin: 20px 0; }
                        </style>
                        <script>
                            // Auto-refresh every 30 seconds to check for new QR codes
                            setTimeout(() => location.reload(), 30000);
                        </script>
                    </head>
                    <body>
                        <div class="container">
                            <h1>🎯 Foco Criador</h1>
                            <h2>📱 Escaneie o QR Code com WhatsApp</h2>

                            <div class="qr-code">
                                <img src="data:image/png;base64,${qrImageBase64}" alt="QR Code" style="max-width: 100%; height: auto;">
                            </div>

                            <div class="instructions">
                                <h3>📋 Instruções:</h3>
                                <p>1. Abra o WhatsApp no seu celular</p>
                                <p>2. Toque nos três pontos (⋮) no canto superior direito</p>
                                <p>3. Selecione "Aparelhos conectados"</p>
                                <p>4. Toque em "Conectar um aparelho"</p>
                                <p>5. Escaneie o QR code acima</p>
                            </div>

                            <a href="/qr" class="btn">🔄 Novo QR Code</a>
                            <a href="javascript:location.reload()" class="btn">↻ Atualizar</a>
                        </div>
                    </body>
                    </html>
                `);

            } catch (error) {
                console.error('❌ Error generating QR image:', error);
                res.writeHead(500, { 'Content-Type': 'text/html; charset=utf-8' });
                res.end(`
                    <!DOCTYPE html>
                    <html>
                    <head>
                        <title>Foco Criador - Erro</title>
                        <meta charset="utf-8">
                        <style>
                            body { font-family: Arial, sans-serif; text-align: center; padding: 50px; background: #f5f5f5; }
                            .container { max-width: 500px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
                            .error { color: #d32f2f; }
                        </style>
                    </head>
                    <body>
                        <div class="container">
                            <h1>🎯 Foco Criador</h1>
                            <h2 class="error">❌ Erro ao gerar QR Code</h2>
                            <p>Ocorreu um erro ao gerar o QR code.</p>
                            <p>Erro: ${error.message}</p>
                            <a href="/qr" style="background: #25D366; color: white; padding: 15px 30px; border: none; border-radius: 5px; text-decoration: none;">🔄 Tentar Novamente</a>
                        </div>
                    </body>
                    </html>
                `);
            }
        } else {
            res.writeHead(404);
            res.end('Not Found');
        }
    });

    const port = process.env.PORT || 8080; // Cloud Run usa 8080 por padrão
    server.listen(port, '0.0.0.0', () => {
        console.log(`🌐 Health check server running on port ${port}`);
        console.log(`🔗 Health check: http://0.0.0.0:${port}/health`);
        console.log(`📱 QR Code: http://0.0.0.0:${port}/qr`);
        console.log(`☁️  Optimized for Google Cloud Run`);
    });
}

// Start the application
async function main() {
    try {
        console.log('🎯 Starting Foco Criador Bot for 24/7 operation...');

        // Setup cloud hosting features
        setupHealthCheck();
        keepAlive();

        global.todoBot = new TodoBotApp();
        await global.todoBot.initialize();

        console.log('🎉 Foco Criador is running 24/7!');
        console.log('📱 Send a message to your WhatsApp to test the bot');
        console.log('🌐 Bot accessible from anywhere with internet');
        console.log('☁️  Ready for Google Cloud Run deployment');

    } catch (error) {
        console.error('❌ Failed to start application:', error);
        process.exit(1);
    }
}

main().catch((error) => {
    console.error('Failed to start application:', error);
    process.exit(1);
});
