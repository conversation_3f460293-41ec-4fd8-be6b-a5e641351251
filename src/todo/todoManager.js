import { Logger } from '../utils/logger.js';
import { DateParser } from '../utils/dateParser.js';
import { Messages } from '../utils/messages.js';

export class TodoManager {
    constructor(sheetsService, calendarService = null) {
        this.sheetsService = sheetsService;
        this.calendarService = calendarService;
        this.logger = new Logger();
        this.dateParser = new DateParser();
        this.maxTodosPerUser = parseInt(process.env.MAX_TODOS_PER_USER) || 100;
    }

    async addTodo(userPhone, todoData) {
        try {
            // Validate input
            if (!todoData.title || todoData.title.trim().length === 0) {
                throw new Error(Messages.ERROR.EMPTY_TITLE);
            }

            // Check user's todo limit
            const existingTodos = await this.sheetsService.getTodos(userPhone);
            if (existingTodos.length >= this.maxTodosPerUser) {
                throw new Error(Messages.formatMaxTodosReached(this.maxTodosPerUser));
            }

            // Clean and validate the todo data
            const cleanTodoData = {
                title: todoData.title.trim(),
                dueDate: todoData.dueDate,
                priority: this.validatePriority(todoData.priority)
            };

            // Add to Google Sheets
            const todo = await this.sheetsService.addTodo(userPhone, cleanTodoData);

            // Add to Google Calendar if calendar service is available and todo has a due date
            if (this.calendarService && todo.dueDate) {
                try {
                    const eventData = {
                        title: todo.title,
                        description: `Tarefa criada via WhatsApp Bot\nPrioridade: ${todo.priority}`,
                        startDate: new Date(todo.dueDate),
                        type: 'todo',
                        userPhone: userPhone
                    };

                    const calendarResult = await this.calendarService.createEvent(eventData);
                    if (calendarResult.success) {
                        // Update the todo with calendar event ID for future reference
                        await this.sheetsService.updateTodo(userPhone, todo.id, {
                            calendarEventId: calendarResult.eventId
                        });
                        this.logger.info(`Todo synced to Google Calendar: ${calendarResult.eventId}`);
                    }
                } catch (calendarError) {
                    this.logger.warn('Failed to sync todo to Google Calendar:', calendarError);
                    // Don't fail the todo creation if calendar sync fails
                }
            }

            this.logger.info(`Added todo for user ${userPhone}: ${todo.title}`);
            return todo;

        } catch (error) {
            this.logger.error('Error adding todo:', error);
            throw error;
        }
    }

    async getTodos(userPhone, filter = {}) {
        try {
            const todos = await this.sheetsService.getTodos(userPhone, filter);
            
            // Sort todos by priority and due date
            return this.sortTodos(todos);

        } catch (error) {
            this.logger.error('Error getting todos:', error);
            throw error;
        }
    }

    async completeTodo(userPhone, todoIdentifier) {
        try {
            const todo = await this.findTodoByIdentifier(userPhone, todoIdentifier);
            if (!todo) {
                return { success: false, error: Messages.ERROR.TODO_NOT_FOUND };
            }

            const result = await this.sheetsService.updateTodo(userPhone, todo.id, { completed: true });
            
            if (result.success) {
                this.logger.info(`Completed todo for user ${userPhone}: ${todo.title}`);
            }
            
            return result;

        } catch (error) {
            this.logger.error('Error completing todo:', error);
            throw error;
        }
    }

    async deleteTodo(userPhone, todoIdentifier) {
        try {
            const todo = await this.findTodoByIdentifier(userPhone, todoIdentifier);
            if (!todo) {
                return { success: false, error: Messages.ERROR.TODO_NOT_FOUND };
            }

            const result = await this.sheetsService.deleteTodo(userPhone, todo.id);
            
            if (result.success) {
                this.logger.info(`Deleted todo for user ${userPhone}: ${todo.title}`);
            }
            
            return result;

        } catch (error) {
            this.logger.error('Error deleting todo:', error);
            throw error;
        }
    }

    async updateTodo(userPhone, todoIdentifier, updates) {
        try {
            const todo = await this.findTodoByIdentifier(userPhone, todoIdentifier);
            if (!todo) {
                return { success: false, error: Messages.ERROR.TODO_NOT_FOUND };
            }

            // Validate updates
            const cleanUpdates = {};
            
            if (updates.title !== undefined) {
                cleanUpdates.title = updates.title.trim();
                if (cleanUpdates.title.length === 0) {
                    throw new Error(Messages.ERROR.EMPTY_TITLE);
                }
            }
            
            if (updates.dueDate !== undefined) {
                cleanUpdates.dueDate = updates.dueDate;
            }
            
            if (updates.priority !== undefined) {
                cleanUpdates.priority = this.validatePriority(updates.priority);
            }
            
            if (updates.completed !== undefined) {
                cleanUpdates.completed = Boolean(updates.completed);
            }

            const result = await this.sheetsService.updateTodo(userPhone, todo.id, cleanUpdates);
            
            if (result.success) {
                this.logger.info(`Updated todo for user ${userPhone}: ${todo.title}`);
            }
            
            return result;

        } catch (error) {
            this.logger.error('Error updating todo:', error);
            throw error;
        }
    }

    async findTodoByIdentifier(userPhone, identifier) {
        try {
            const todos = await this.sheetsService.getTodos(userPhone);
            
            // Try exact title match first
            let todo = todos.find(t => t.title.toLowerCase() === identifier.toLowerCase());
            if (todo) return todo;

            // Try partial title match
            todo = todos.find(t => t.title.toLowerCase().includes(identifier.toLowerCase()));
            if (todo) return todo;

            // Try to match by index (e.g., "first", "second", "1", "2")
            const indexMatch = this.parseIndexIdentifier(identifier);
            if (indexMatch !== null && indexMatch >= 0 && indexMatch < todos.length) {
                const sortedTodos = this.sortTodos(todos.filter(t => !t.completed));
                return sortedTodos[indexMatch];
            }

            return null;

        } catch (error) {
            this.logger.error('Error finding todo by identifier:', error);
            return null;
        }
    }

    parseIndexIdentifier(identifier) {
        const text = identifier.toLowerCase().trim();
        
        // Handle numeric indices
        const numMatch = text.match(/^(\d+)(?:st|nd|rd|th)?$/);
        if (numMatch) {
            return parseInt(numMatch[1]) - 1; // Convert to 0-based index
        }

        // Handle word indices
        const wordIndices = {
            'first': 0, '1st': 0,
            'second': 1, '2nd': 1,
            'third': 2, '3rd': 2,
            'fourth': 3, '4th': 3,
            'fifth': 4, '5th': 4,
            'last': -1
        };

        return wordIndices[text] !== undefined ? wordIndices[text] : null;
    }

    sortTodos(todos) {
        return todos.sort((a, b) => {
            // Incomplete todos first
            if (a.completed !== b.completed) {
                return a.completed ? 1 : -1;
            }

            // Then by due date (overdue first, then by date)
            if (a.dueDate && b.dueDate) {
                const dateA = new Date(a.dueDate);
                const dateB = new Date(b.dueDate);
                return dateA - dateB;
            }
            if (a.dueDate && !b.dueDate) return -1;
            if (!a.dueDate && b.dueDate) return 1;

            // Then by priority
            const priorityOrder = { 'high': 0, 'medium': 1, 'low': 2 };
            const priorityDiff = priorityOrder[a.priority] - priorityOrder[b.priority];
            if (priorityDiff !== 0) return priorityDiff;

            // Finally by creation date (newest first)
            return new Date(b.createdAt) - new Date(a.createdAt);
        });
    }

    validatePriority(priority) {
        const validPriorities = ['high', 'medium', 'low'];
        return validPriorities.includes(priority) ? priority : 'medium';
    }

    async getUserStats(userPhone) {
        try {
            const todos = await this.sheetsService.getTodos(userPhone);
            
            const stats = {
                total: todos.length,
                completed: todos.filter(t => t.completed).length,
                pending: todos.filter(t => !t.completed).length,
                overdue: todos.filter(t => !t.completed && t.dueDate && this.dateParser.isOverdue(t.dueDate)).length,
                dueToday: todos.filter(t => !t.completed && t.dueDate && this.dateParser.isToday(t.dueDate)).length
            };

            return stats;

        } catch (error) {
            this.logger.error('Error getting user stats:', error);
            throw error;
        }
    }

    async getOverdueTodos(userPhone) {
        try {
            const todos = await this.sheetsService.getTodos(userPhone);
            return todos.filter(t => !t.completed && t.dueDate && this.dateParser.isOverdue(t.dueDate));

        } catch (error) {
            this.logger.error('Error getting overdue todos:', error);
            throw error;
        }
    }

    async getTodaysTodos(userPhone) {
        try {
            const todos = await this.sheetsService.getTodos(userPhone);
            return todos.filter(t => !t.completed && t.dueDate && this.dateParser.isToday(t.dueDate));

        } catch (error) {
            this.logger.error('Error getting today\'s todos:', error);
            throw error;
        }
    }
}
