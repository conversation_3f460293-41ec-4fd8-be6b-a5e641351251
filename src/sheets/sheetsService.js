import { google } from 'googleapis';
import fs from 'fs';
import { Logger } from '../utils/logger.js';

export class SheetsService {
    constructor() {
        this.logger = new Logger();
        this.sheets = null;
        this.spreadsheetId = process.env.GOOGLE_SHEETS_ID;
        this.sheetName = 'Todos';
        this.interactionsSheetName = 'Interactions';
        this.foundationsSheetName = 'Foundations';
        this.questsSheetName = 'Quests';
    }

    async initialize() {
        try {
            // Load service account credentials
            const keyPath = process.env.GOOGLE_SERVICE_ACCOUNT_KEY_PATH;
            if (!keyPath || !fs.existsSync(keyPath)) {
                throw new Error('Google service account key file not found. Please check GOOGLE_SERVICE_ACCOUNT_KEY_PATH');
            }

            const credentials = JSON.parse(fs.readFileSync(keyPath, 'utf8'));

            // Create JWT auth client
            const auth = new google.auth.JWT(
                credentials.client_email,
                null,
                credentials.private_key,
                ['https://www.googleapis.com/auth/spreadsheets']
            );

            // Initialize the sheets API
            this.sheets = google.sheets({ version: 'v4', auth });

            // Ensure all sheets exist and have proper headers
            await this.ensureAllSheetsSetup();

            this.logger.info('Google Sheets service initialized successfully');
        } catch (error) {
            this.logger.error('Failed to initialize Google Sheets service:', error);
            throw error;
        }
    }

    async ensureAllSheetsSetup() {
        try {
            // Check if the spreadsheet exists
            const spreadsheet = await this.sheets.spreadsheets.get({
                spreadsheetId: this.spreadsheetId
            });

            const existingSheets = spreadsheet.data.sheets.map(s => s.properties.title);
            const requiredSheets = [
                this.sheetName,
                this.interactionsSheetName,
                this.foundationsSheetName,
                this.questsSheetName
            ];

            // Create missing sheets
            const sheetsToCreate = requiredSheets.filter(name => !existingSheets.includes(name));

            if (sheetsToCreate.length > 0) {
                const requests = sheetsToCreate.map(sheetName => ({
                    addSheet: {
                        properties: {
                            title: sheetName
                        }
                    }
                }));

                await this.sheets.spreadsheets.batchUpdate({
                    spreadsheetId: this.spreadsheetId,
                    resource: { requests }
                });

                this.logger.info(`Created sheets: ${sheetsToCreate.join(', ')}`);
            }

            // Ensure headers are set for all sheets
            await this.ensureTodosHeaders();
            await this.ensureInteractionsHeaders();
            await this.ensureFoundationsHeaders();
            await this.ensureQuestsHeaders();

        } catch (error) {
            this.logger.error('Error setting up sheets:', error);
            throw error;
        }
    }

    async ensureSheetSetup() {
        // Legacy method - redirect to new comprehensive setup
        await this.ensureAllSheetsSetup();
    }

    async ensureTodosHeaders() {
        const headers = ['ID', 'User Phone', 'Title', 'Due Date', 'Priority', 'Completed', 'Created At', 'Updated At'];
        await this.ensureSheetHeaders(this.sheetName, headers, 'A1:H1');
    }

    async ensureInteractionsHeaders() {
        const headers = [
            'ID', 'User Phone', 'User Name', 'Message Text', 'Intent Action',
            'Intent Data', 'Response Text', 'Success', 'Error Message',
            'Processing Time (ms)', 'Timestamp'
        ];
        await this.ensureSheetHeaders(this.interactionsSheetName, headers, 'A1:K1');
    }

    async ensureFoundationsHeaders() {
        const headers = [
            'ID', 'User Phone', 'Foundation Name', 'Description', 'Target Score',
            'Current Score', 'Status', 'Created At', 'Updated At'
        ];
        await this.ensureSheetHeaders(this.foundationsSheetName, headers, 'A1:I1');
    }

    async ensureQuestsHeaders() {
        const headers = [
            'ID', 'User Phone', 'Quest Title', 'Description', 'Start Date',
            'Due Date', 'Status', 'Progress Percentage', 'Weekly Goal',
            'Current Week', 'Measurement Metric', 'Created At', 'Updated At'
        ];
        await this.ensureSheetHeaders(this.questsSheetName, headers, 'A1:M1');
    }

    async ensureSheetHeaders(sheetName, headers, range) {
        try {
            // Check if headers exist
            const response = await this.sheets.spreadsheets.values.get({
                spreadsheetId: this.spreadsheetId,
                range: `${sheetName}!${range}`
            });

            if (!response.data.values || response.data.values.length === 0) {
                // Add headers
                await this.sheets.spreadsheets.values.update({
                    spreadsheetId: this.spreadsheetId,
                    range: `${sheetName}!${range}`,
                    valueInputOption: 'RAW',
                    resource: {
                        values: [headers]
                    }
                });
                this.logger.info(`Added headers to ${sheetName} sheet`);
            }
        } catch (error) {
            this.logger.error(`Error ensuring headers for ${sheetName}:`, error);
            throw error;
        }
    }

    // Legacy method for backward compatibility
    async ensureHeaders() {
        await this.ensureTodosHeaders();
    }

    async addTodo(userPhone, todoData) {
        try {
            const id = this.generateId();
            const now = new Date().toISOString();
            
            const row = [
                id,
                userPhone,
                todoData.title,
                todoData.dueDate || '',
                todoData.priority || 'medium',
                'FALSE',
                now,
                now
            ];

            await this.sheets.spreadsheets.values.append({
                spreadsheetId: this.spreadsheetId,
                range: `${this.sheetName}!A:H`,
                valueInputOption: 'RAW',
                resource: {
                    values: [row]
                }
            });

            return {
                id,
                userPhone,
                title: todoData.title,
                dueDate: todoData.dueDate,
                priority: todoData.priority || 'medium',
                completed: false,
                createdAt: now,
                updatedAt: now
            };

        } catch (error) {
            this.logger.error('Error adding todo to sheet:', error);
            throw error;
        }
    }

    async getTodos(userPhone, filter = {}) {
        try {
            const response = await this.sheets.spreadsheets.values.get({
                spreadsheetId: this.spreadsheetId,
                range: `${this.sheetName}!A:H`
            });

            if (!response.data.values || response.data.values.length <= 1) {
                return [];
            }

            const [, ...rows] = response.data.values;
            const todos = rows
                .filter(row => row[1] === userPhone) // Filter by user phone
                .map(row => ({
                    id: row[0],
                    userPhone: row[1],
                    title: row[2],
                    dueDate: row[3] || null,
                    priority: row[4] || 'medium',
                    completed: row[5] === 'TRUE',
                    createdAt: row[6],
                    updatedAt: row[7]
                }));

            return this.applyFilter(todos, filter);

        } catch (error) {
            this.logger.error('Error getting todos from sheet:', error);
            throw error;
        }
    }

    async updateTodo(userPhone, todoId, updates) {
        try {
            const rowIndex = await this.findTodoRowIndex(userPhone, todoId);
            if (rowIndex === -1) {
                return { success: false, error: 'Todo not found' };
            }

            const range = `${this.sheetName}!A${rowIndex}:H${rowIndex}`;
            const response = await this.sheets.spreadsheets.values.get({
                spreadsheetId: this.spreadsheetId,
                range
            });

            const row = response.data.values[0];
            const now = new Date().toISOString();

            // Update specific fields
            if (updates.title !== undefined) row[2] = updates.title;
            if (updates.dueDate !== undefined) row[3] = updates.dueDate || '';
            if (updates.priority !== undefined) row[4] = updates.priority;
            if (updates.completed !== undefined) row[5] = updates.completed ? 'TRUE' : 'FALSE';
            row[7] = now; // Updated at

            await this.sheets.spreadsheets.values.update({
                spreadsheetId: this.spreadsheetId,
                range,
                valueInputOption: 'RAW',
                resource: {
                    values: [row]
                }
            });

            return {
                success: true,
                todo: {
                    id: row[0],
                    userPhone: row[1],
                    title: row[2],
                    dueDate: row[3] || null,
                    priority: row[4],
                    completed: row[5] === 'TRUE',
                    createdAt: row[6],
                    updatedAt: row[7]
                }
            };

        } catch (error) {
            this.logger.error('Error updating todo in sheet:', error);
            throw error;
        }
    }

    async deleteTodo(userPhone, todoId) {
        try {
            const rowIndex = await this.findTodoRowIndex(userPhone, todoId);
            if (rowIndex === -1) {
                return { success: false, error: 'Todo not found' };
            }

            // Get the todo data before deletion
            const range = `${this.sheetName}!A${rowIndex}:H${rowIndex}`;
            const response = await this.sheets.spreadsheets.values.get({
                spreadsheetId: this.spreadsheetId,
                range
            });

            const row = response.data.values[0];
            const todo = {
                id: row[0],
                title: row[2]
            };

            // Delete the row
            await this.sheets.spreadsheets.batchUpdate({
                spreadsheetId: this.spreadsheetId,
                resource: {
                    requests: [{
                        deleteDimension: {
                            range: {
                                sheetId: await this.getSheetId(),
                                dimension: 'ROWS',
                                startIndex: rowIndex - 1,
                                endIndex: rowIndex
                            }
                        }
                    }]
                }
            });

            return { success: true, todo };

        } catch (error) {
            this.logger.error('Error deleting todo from sheet:', error);
            throw error;
        }
    }

    async findTodoRowIndex(userPhone, todoId) {
        try {
            const response = await this.sheets.spreadsheets.values.get({
                spreadsheetId: this.spreadsheetId,
                range: `${this.sheetName}!A:B`
            });

            if (!response.data.values) return -1;

            for (let i = 1; i < response.data.values.length; i++) {
                const row = response.data.values[i];
                if (row[0] === todoId && row[1] === userPhone) {
                    return i + 1; // Sheet rows are 1-indexed
                }
            }

            return -1;
        } catch (error) {
            this.logger.error('Error finding todo row:', error);
            return -1;
        }
    }

    async getSheetId() {
        const spreadsheet = await this.sheets.spreadsheets.get({
            spreadsheetId: this.spreadsheetId
        });

        const sheet = spreadsheet.data.sheets.find(s => s.properties.title === this.sheetName);
        return sheet ? sheet.properties.sheetId : 0;
    }

    applyFilter(todos, filter) {
        return todos.filter(todo => {
            if (filter.completed !== undefined && todo.completed !== filter.completed) {
                return false;
            }
            
            if (filter.overdue && (!todo.dueDate || !this.isOverdue(todo.dueDate))) {
                return false;
            }
            
            if (filter.today && (!todo.dueDate || !this.isToday(todo.dueDate))) {
                return false;
            }
            
            return true;
        });
    }

    isOverdue(dateString) {
        if (!dateString) return false;
        const date = new Date(dateString);
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        return date < today;
    }

    isToday(dateString) {
        if (!dateString) return false;
        const date = new Date(dateString);
        const today = new Date();
        return date.toDateString() === today.toDateString();
    }

    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substring(2);
    }

    // Interaction logging methods
    async logInteraction(interactionData) {
        try {
            const id = this.generateId();
            const now = new Date().toISOString();

            const row = [
                id,
                interactionData.userPhone,
                interactionData.userName || '',
                interactionData.messageText,
                interactionData.intentAction,
                JSON.stringify(interactionData.intentData || {}),
                interactionData.responseText,
                interactionData.success ? 'TRUE' : 'FALSE',
                interactionData.errorMessage || '',
                interactionData.processingTime || 0,
                now
            ];

            await this.sheets.spreadsheets.values.append({
                spreadsheetId: this.spreadsheetId,
                range: `${this.interactionsSheetName}!A:K`,
                valueInputOption: 'RAW',
                resource: {
                    values: [row]
                }
            });

            return { success: true, id };

        } catch (error) {
            this.logger.error('Error logging interaction to sheet:', error);
            return { success: false, error: error.message };
        }
    }

    // Foundation management methods
    async addFoundation(userPhone, foundationData) {
        try {
            const id = this.generateId();
            const now = new Date().toISOString();

            const row = [
                id,
                userPhone,
                foundationData.name,
                foundationData.description || '',
                foundationData.targetScore || 10,
                foundationData.currentScore || 0,
                foundationData.status || 'active',
                now,
                now
            ];

            await this.sheets.spreadsheets.values.append({
                spreadsheetId: this.spreadsheetId,
                range: `${this.foundationsSheetName}!A:I`,
                valueInputOption: 'RAW',
                resource: {
                    values: [row]
                }
            });

            return {
                id,
                userPhone,
                name: foundationData.name,
                description: foundationData.description,
                targetScore: foundationData.targetScore || 10,
                currentScore: foundationData.currentScore || 0,
                status: foundationData.status || 'active',
                createdAt: now,
                updatedAt: now
            };

        } catch (error) {
            this.logger.error('Error adding foundation to sheet:', error);
            throw error;
        }
    }

    async getFoundations(userPhone) {
        try {
            const response = await this.sheets.spreadsheets.values.get({
                spreadsheetId: this.spreadsheetId,
                range: `${this.foundationsSheetName}!A:I`
            });

            if (!response.data.values || response.data.values.length <= 1) {
                return [];
            }

            const [, ...rows] = response.data.values;
            return rows
                .filter(row => row[1] === userPhone)
                .map(row => ({
                    id: row[0],
                    userPhone: row[1],
                    name: row[2],
                    description: row[3] || '',
                    targetScore: parseInt(row[4]) || 10,
                    currentScore: parseInt(row[5]) || 0,
                    status: row[6] || 'active',
                    createdAt: row[7],
                    updatedAt: row[8]
                }));

        } catch (error) {
            this.logger.error('Error getting foundations from sheet:', error);
            throw error;
        }
    }

    // Quest management methods
    async addQuest(userPhone, questData) {
        try {
            const id = this.generateId();
            const now = new Date().toISOString();

            const row = [
                id,
                userPhone,
                questData.title,
                questData.description || '',
                questData.startDate || now.split('T')[0],
                questData.dueDate,
                questData.status || 'active',
                questData.progressPercentage || 0,
                questData.weeklyGoal || '',
                questData.currentWeek || 1,
                questData.measurementMetric || '',
                now,
                now
            ];

            await this.sheets.spreadsheets.values.append({
                spreadsheetId: this.spreadsheetId,
                range: `${this.questsSheetName}!A:M`,
                valueInputOption: 'RAW',
                resource: {
                    values: [row]
                }
            });

            return {
                id,
                userPhone,
                title: questData.title,
                description: questData.description,
                startDate: questData.startDate || now.split('T')[0],
                dueDate: questData.dueDate,
                status: questData.status || 'active',
                progressPercentage: questData.progressPercentage || 0,
                weeklyGoal: questData.weeklyGoal,
                currentWeek: questData.currentWeek || 1,
                measurementMetric: questData.measurementMetric,
                createdAt: now,
                updatedAt: now
            };

        } catch (error) {
            this.logger.error('Error adding quest to sheet:', error);
            throw error;
        }
    }

    async getQuests(userPhone) {
        try {
            const response = await this.sheets.spreadsheets.values.get({
                spreadsheetId: this.spreadsheetId,
                range: `${this.questsSheetName}!A:M`
            });

            if (!response.data.values || response.data.values.length <= 1) {
                return [];
            }

            const [, ...rows] = response.data.values;
            return rows
                .filter(row => row[1] === userPhone)
                .map(row => ({
                    id: row[0],
                    userPhone: row[1],
                    title: row[2],
                    description: row[3] || '',
                    startDate: row[4],
                    dueDate: row[5],
                    status: row[6] || 'active',
                    progressPercentage: parseInt(row[7]) || 0,
                    weeklyGoal: row[8] || '',
                    currentWeek: parseInt(row[9]) || 1,
                    measurementMetric: row[10] || '',
                    createdAt: row[11],
                    updatedAt: row[12]
                }));

        } catch (error) {
            this.logger.error('Error getting quests from sheet:', error);
            throw error;
        }
    }

    async updateFoundation(foundationId, updates) {
        try {
            // Get all foundations to find the row
            const response = await this.sheets.spreadsheets.values.get({
                spreadsheetId: this.spreadsheetId,
                range: `${this.foundationsSheetName}!A:I`
            });

            if (!response.data.values || response.data.values.length <= 1) {
                throw new Error('Foundation not found');
            }

            const [headers, ...rows] = response.data.values;
            const rowIndex = rows.findIndex(row => row[0] === foundationId);

            if (rowIndex === -1) {
                throw new Error('Foundation not found');
            }

            const row = [...rows[rowIndex]];
            const now = new Date().toISOString();

            // Update fields
            if (updates.name !== undefined) row[2] = updates.name;
            if (updates.description !== undefined) row[3] = updates.description;
            if (updates.targetScore !== undefined) row[4] = updates.targetScore;
            if (updates.currentScore !== undefined) row[5] = updates.currentScore;
            if (updates.status !== undefined) row[6] = updates.status;
            row[8] = now; // Updated at

            const range = `${this.foundationsSheetName}!A${rowIndex + 2}:I${rowIndex + 2}`;
            await this.sheets.spreadsheets.values.update({
                spreadsheetId: this.spreadsheetId,
                range,
                valueInputOption: 'RAW',
                resource: {
                    values: [row]
                }
            });

            return { success: true };

        } catch (error) {
            this.logger.error('Error updating foundation in sheet:', error);
            throw error;
        }
    }

    async updateQuest(questId, updates) {
        try {
            // Get all quests to find the row
            const response = await this.sheets.spreadsheets.values.get({
                spreadsheetId: this.spreadsheetId,
                range: `${this.questsSheetName}!A:M`
            });

            if (!response.data.values || response.data.values.length <= 1) {
                throw new Error('Quest not found');
            }

            const [headers, ...rows] = response.data.values;
            const rowIndex = rows.findIndex(row => row[0] === questId);

            if (rowIndex === -1) {
                throw new Error('Quest not found');
            }

            const row = [...rows[rowIndex]];
            const now = new Date().toISOString();

            // Update fields
            if (updates.title !== undefined) row[2] = updates.title;
            if (updates.description !== undefined) row[3] = updates.description;
            if (updates.startDate !== undefined) row[4] = updates.startDate;
            if (updates.dueDate !== undefined) row[5] = updates.dueDate;
            if (updates.status !== undefined) row[6] = updates.status;
            if (updates.progressPercentage !== undefined) row[7] = updates.progressPercentage;
            if (updates.weeklyGoal !== undefined) row[8] = updates.weeklyGoal;
            if (updates.currentWeek !== undefined) row[9] = updates.currentWeek;
            if (updates.measurementMetric !== undefined) row[10] = updates.measurementMetric;
            row[12] = now; // Updated at

            const range = `${this.questsSheetName}!A${rowIndex + 2}:M${rowIndex + 2}`;
            await this.sheets.spreadsheets.values.update({
                spreadsheetId: this.spreadsheetId,
                range,
                valueInputOption: 'RAW',
                resource: {
                    values: [row]
                }
            });

            return { success: true };

        } catch (error) {
            this.logger.error('Error updating quest in sheet:', error);
            throw error;
        }
    }
}
