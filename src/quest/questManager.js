import { Logger } from '../utils/logger.js';
import { Messages } from '../utils/messages.js';
import { DateParser } from '../utils/dateParser.js';

export class QuestManager {
    constructor(sheetsService) {
        this.sheetsService = sheetsService;
        this.logger = new Logger();
        this.dateParser = new DateParser();
    }

    async addQuest(userPhone, questData) {
        try {
            // Validate quest data
            if (!questData.title || questData.title.trim() === '') {
                throw new Error('Quest title is required');
            }

            // Parse and validate dates
            const startDate = questData.startDate || new Date().toISOString().split('T')[0];
            let dueDate = questData.dueDate;
            
            if (dueDate) {
                dueDate = this.dateParser.parseDate(dueDate);
                if (!dueDate) {
                    throw new Error('Invalid due date format');
                }
            } else {
                // Default to 3 months from start date
                const start = new Date(startDate);
                start.setMonth(start.getMonth() + 3);
                dueDate = start.toISOString().split('T')[0];
            }

            // Validate date range
            if (new Date(dueDate) <= new Date(startDate)) {
                throw new Error('Due date must be after start date');
            }

            // Check if quest already exists for this user
            const existingQuests = await this.getQuests(userPhone);
            const duplicate = existingQuests.find(q => 
                q.title.toLowerCase() === questData.title.toLowerCase() && 
                q.status === 'active'
            );

            if (duplicate) {
                throw new Error(`Active quest "${questData.title}" already exists`);
            }

            const quest = await this.sheetsService.addQuest(userPhone, {
                title: questData.title.trim(),
                description: questData.description || '',
                startDate,
                dueDate,
                status: 'active',
                progressPercentage: 0,
                weeklyGoal: questData.weeklyGoal || '',
                currentWeek: 1,
                measurementMetric: questData.measurementMetric || ''
            });

            this.logger.info(`Quest added for user ${userPhone}: ${quest.title}`);
            return quest;

        } catch (error) {
            this.logger.error('Error adding quest:', error);
            throw error;
        }
    }

    async getQuests(userPhone, filter = {}) {
        try {
            let quests = await this.sheetsService.getQuests(userPhone);

            // Apply filters
            if (filter.status) {
                quests = quests.filter(q => q.status === filter.status);
            }

            if (filter.active !== undefined) {
                quests = quests.filter(q => q.status === 'active');
            }

            if (filter.overdue) {
                const today = new Date().toISOString().split('T')[0];
                quests = quests.filter(q => q.dueDate < today && q.status === 'active');
            }

            // Sort by creation date (newest first)
            quests.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

            return quests;

        } catch (error) {
            this.logger.error('Error getting quests:', error);
            throw error;
        }
    }

    async updateQuestProgress(userPhone, questIdentifier, progressPercentage) {
        try {
            const quests = await this.getQuests(userPhone);
            const quest = this.findQuest(quests, questIdentifier);

            if (!quest) {
                return { success: false, error: 'Quest not found' };
            }

            // Validate progress
            if (progressPercentage < 0 || progressPercentage > 100) {
                return { 
                    success: false, 
                    error: 'Progress must be between 0 and 100%' 
                };
            }

            // Calculate current week based on start date
            const startDate = new Date(quest.startDate);
            const today = new Date();
            const weeksPassed = Math.floor((today - startDate) / (7 * 24 * 60 * 60 * 1000)) + 1;

            // Update quest progress in sheets
            await this.sheetsService.updateQuest(quest.id, {
                progressPercentage,
                currentWeek: Math.max(weeksPassed, 1),
                status: progressPercentage >= 100 ? 'completed' : 'active'
            });

            quest.progressPercentage = progressPercentage;
            quest.currentWeek = Math.max(weeksPassed, 1);
            quest.status = progressPercentage >= 100 ? 'completed' : 'active';
            
            this.logger.info(`Quest progress updated for user ${userPhone}: ${quest.title} = ${progressPercentage}%`);
            return { success: true, quest };

        } catch (error) {
            this.logger.error('Error updating quest progress:', error);
            return { success: false, error: error.message };
        }
    }

    async getQuestStats(userPhone) {
        try {
            const quests = await this.getQuests(userPhone);
            const today = new Date().toISOString().split('T')[0];
            
            const stats = {
                total: quests.length,
                active: quests.filter(q => q.status === 'active').length,
                completed: quests.filter(q => q.status === 'completed').length,
                overdue: quests.filter(q => q.dueDate < today && q.status === 'active').length,
                averageProgress: 0,
                questDetails: []
            };

            if (quests.length > 0) {
                const totalProgress = quests.reduce((sum, q) => sum + q.progressPercentage, 0);
                stats.averageProgress = Math.round(totalProgress / quests.length);

                stats.questDetails = quests.map(q => ({
                    title: q.title,
                    progress: q.progressPercentage,
                    status: q.status,
                    dueDate: q.dueDate,
                    currentWeek: q.currentWeek,
                    isOverdue: q.dueDate < today && q.status === 'active'
                }));
            }

            return stats;

        } catch (error) {
            this.logger.error('Error getting quest stats:', error);
            throw error;
        }
    }

    findQuest(quests, identifier) {
        // Try to find by ID first
        let quest = quests.find(q => q.id === identifier);
        
        if (!quest) {
            // Try to find by title (case insensitive)
            quest = quests.find(q => 
                q.title.toLowerCase().includes(identifier.toLowerCase())
            );
        }

        if (!quest) {
            // Try to find by index (1-based)
            const index = parseInt(identifier);
            if (!isNaN(index) && index > 0 && index <= quests.length) {
                quest = quests[index - 1];
            }
        }

        return quest;
    }

    formatQuestsList(quests) {
        if (quests.length === 0) {
            return Messages.QUESTS.NO_QUESTS;
        }

        let response = Messages.QUESTS.LIST_HEADER;
        
        quests.forEach((quest, index) => {
            const progressBar = this.generateProgressBar(quest.progressPercentage);
            const statusEmoji = this.getStatusEmoji(quest);
            const daysLeft = this.calculateDaysLeft(quest.dueDate);
            
            response += `${index + 1}. ${statusEmoji} **${quest.title}**\n`;
            response += `   📊 ${quest.progressPercentage}% ${progressBar}\n`;
            response += `   📅 Semana ${quest.currentWeek} | ${daysLeft}\n`;
            if (quest.description) {
                response += `   📝 ${quest.description}\n`;
            }
            response += '\n';
        });

        return response;
    }

    generateProgressBar(percentage, length = 10) {
        const filled = Math.round((percentage / 100) * length);
        const empty = length - filled;
        return '█'.repeat(filled) + '░'.repeat(empty);
    }

    getStatusEmoji(quest) {
        if (quest.status === 'completed') return '✅';
        if (quest.status === 'paused') return '⏸️';
        
        const today = new Date().toISOString().split('T')[0];
        if (quest.dueDate < today) return '🔴'; // Overdue
        
        return '🟡'; // Active
    }

    calculateDaysLeft(dueDate) {
        const today = new Date();
        const due = new Date(dueDate);
        const diffTime = due - today;
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        
        if (diffDays < 0) {
            return `${Math.abs(diffDays)} dias atrasado`;
        } else if (diffDays === 0) {
            return 'Vence hoje';
        } else if (diffDays === 1) {
            return 'Vence amanhã';
        } else {
            return `${diffDays} dias restantes`;
        }
    }
}
