import { createRequire } from 'module';
const require = createRequire(import.meta.url);
const { default: makeWASocket, DisconnectReason, useMultiFileAuthState, fetchLatestBaileysVersion } = require('@whiskeysockets/baileys');
import { Logger } from '../utils/logger.js';
import { Messages } from '../utils/messages.js';
import fs from 'fs';

// Global singleton to prevent multiple instances
let globalBotInstance = null;
let isGloballyInitializing = false;

export class StableWhatsAppBot {
    constructor(todoManager, aiProcessor, sheetsService, foundationManager, questManager) {
        // Enforce singleton pattern
        if (globalBotInstance) {
            return globalBotInstance;
        }

        this.todoManager = todoManager;
        this.aiProcessor = aiProcessor;
        this.sheetsService = sheetsService;
        this.foundationManager = foundationManager;
        this.questManager = questManager;
        this.logger = new Logger();
        this.sock = null;
        this.isReady = false;
        this.authDir = './baileys_auth';
        this.currentQRCode = null;
        this.connectionAttempts = 0;
        this.maxRetries = 3;
        this.isConnected = false;

        globalBotInstance = this;
    }

    async initialize() {
        // Global lock to prevent multiple initializations
        if (isGloballyInitializing) {
            this.logger.info('🔒 Global initialization in progress, waiting...');
            return;
        }

        if (this.isConnected && this.sock) {
            this.logger.info('✅ Already connected, skipping initialization');
            return;
        }

        isGloballyInitializing = true;

        try {
            this.logger.info('🚀 Starting stable WhatsApp connection...');

            // Clean up any existing connection
            await this.cleanup();

            // Ensure auth directory exists
            if (!fs.existsSync(this.authDir)) {
                fs.mkdirSync(this.authDir, { recursive: true });
            }

            // Get auth state
            const { state, saveCreds } = await useMultiFileAuthState(this.authDir);

            // Wrap saveCreds with error handling
            const safeSaveCreds = async () => {
                try {
                    await saveCreds();
                } catch (error) {
                    this.logger.error('Failed to save credentials:', error.message);
                }
            };

            // Get Baileys version
            const { version, isLatest } = await fetchLatestBaileysVersion();
            this.logger.info(`Using Baileys version ${version}, latest: ${isLatest}`);

            // Create socket with minimal configuration
            this.sock = makeWASocket({
                version,
                auth: state,
                printQRInTerminal: false,
                logger: {
                    level: 'silent',
                    child: () => ({ level: 'silent', trace: () => {}, debug: () => {}, info: () => {}, warn: () => {}, error: () => {}, fatal: () => {} }),
                    trace: () => {}, debug: () => {}, info: () => {}, warn: () => {}, error: () => {}, fatal: () => {}
                },
                browser: ['Foco Criador Bot', 'Chrome', '1.0.0'],
                connectTimeoutMs: 30000,
                defaultQueryTimeoutMs: 30000,
                keepAliveIntervalMs: 25000,
                markOnlineOnConnect: false,
                syncFullHistory: false
            });

            // Setup event handlers
            this.setupEventHandlers(safeSaveCreds);

            this.logger.info('✅ WhatsApp socket created successfully');

        } catch (error) {
            this.logger.error('❌ Failed to initialize WhatsApp:', error.message);
            this.connectionAttempts++;
            
            if (this.connectionAttempts < this.maxRetries) {
                this.logger.info(`🔄 Retrying in 15 seconds... (${this.connectionAttempts}/${this.maxRetries})`);
                setTimeout(() => {
                    isGloballyInitializing = false;
                    this.initialize();
                }, 15000);
            } else {
                this.logger.error('❌ Max retries reached, giving up');
                isGloballyInitializing = false;
            }
        }
    }

    async cleanup() {
        if (this.sock) {
            try {
                this.logger.info('🧹 Cleaning up existing connection...');
                this.sock.end();
                this.sock = null;
                this.isConnected = false;
                this.isReady = false;
                await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds
            } catch (error) {
                this.logger.warn('Warning during cleanup:', error.message);
            }
        }
    }

    setupEventHandlers(saveCreds) {
        this.sock.ev.on('connection.update', async (update) => {
            const { connection, lastDisconnect, qr } = update;

            if (qr) {
                this.currentQRCode = qr;
                this.logger.info('📱 QR Code received! Visit the web interface to scan:');
                this.logger.info('🌐 QR Code URL: https://fococriador-6a2gqq6k7a-uc.a.run.app/qr-image');
                console.log('📱 QR Code received! Visit the web interface to scan:');
                console.log('🌐 QR Code URL: https://fococriador-6a2gqq6k7a-uc.a.run.app/qr-image');
                console.log('======================================================================');
                this.logger.info(`Raw QR data: ${qr}`);
            }

            if (connection === 'close') {
                const shouldReconnect = (lastDisconnect?.error)?.output?.statusCode !== DisconnectReason.loggedOut;
                this.logger.info(`Connection closed due to: ${lastDisconnect?.error} , reconnecting: ${shouldReconnect}`);
                
                this.isConnected = false;
                this.isReady = false;
                isGloballyInitializing = false;

                if (shouldReconnect) {
                    setTimeout(() => this.initialize(), 5000);
                }
            } else if (connection === 'open') {
                this.logger.info('🎉 WhatsApp connection opened successfully!');
                this.isConnected = true;
                this.isReady = true;
                this.connectionAttempts = 0; // Reset attempts on successful connection
                isGloballyInitializing = false;
            }
        });

        this.sock.ev.on('creds.update', saveCreds);

        this.sock.ev.on('messages.upsert', async (m) => {
            this.logger.info(`📨 Raw message event received (ready: ${this.isReady}):`, JSON.stringify(m, null, 2));

            if (!this.isReady) {
                this.logger.warn('⚠️ Bot not ready, skipping message');
                return;
            }

            const message = m.messages[0];
            if (!message) {
                this.logger.warn('⚠️ No message in upsert event');
                return;
            }

            this.logger.info(`📱 Message details:`, {
                fromMe: message.key.fromMe,
                remoteJid: message.key.remoteJid,
                hasMessage: !!message.message,
                messageType: message.message ? Object.keys(message.message)[0] : 'none'
            });

            if (!message.key.fromMe && message.message) {
                try {
                    this.logger.info(`✅ Processing message from ${message.key.remoteJid}`);
                    await this.handleMessage(message);
                } catch (error) {
                    this.logger.error('Error handling message:', error.message);
                }
            } else {
                this.logger.info(`⏭️ Skipping message: fromMe=${message.key.fromMe}, hasMessage=${!!message.message}`);
            }
        });
    }

    async handleMessage(message) {
        const messageText = this.extractMessageText(message);
        const from = message.key.remoteJid;

        this.logger.info(`📨 Message from ${from}: ${messageText}`);

        // Record interaction in Google Sheets
        if (this.sheetsService) {
            try {
                await this.sheetsService.recordInteraction(from, messageText, 'received');
            } catch (error) {
                this.logger.error('Failed to record interaction:', error.message);
            }
        }

        // Process message and get response
        let response;
        try {
            response = await this.processMessage(messageText, from);
        } catch (error) {
            this.logger.error('Error processing message:', error.message);
            response = Messages.ERROR_PROCESSING;
        }

        // Send response
        if (response) {
            await this.sendMessage(from, response);
        }
    }

    extractMessageText(message) {
        return message.message?.conversation || 
               message.message?.extendedTextMessage?.text || 
               '';
    }

    async processMessage(text, from) {
        const normalizedText = text.toLowerCase().trim();

        // Basic commands
        if (normalizedText === 'oi' || normalizedText === 'olá' || normalizedText === 'hello') {
            return Messages.WELCOME;
        }

        if (normalizedText === 'ajuda' || normalizedText === 'help') {
            return Messages.HELP;
        }

        if (normalizedText === 'status') {
            return Messages.STATUS_READY;
        }

        // Use AI for complex queries
        if (this.aiProcessor) {
            try {
                return await this.aiProcessor.processMessage(text, from);
            } catch (error) {
                this.logger.error('AI processing failed:', error.message);
                return Messages.AI_ERROR;
            }
        }

        return Messages.UNKNOWN_COMMAND;
    }

    async sendMessage(to, text) {
        if (!this.sock || !this.isReady) {
            this.logger.warn('Cannot send message: bot not ready');
            return;
        }

        try {
            await this.sock.sendMessage(to, { text });
            this.logger.info(`📤 Sent message to ${to}: ${text.substring(0, 50)}...`);

            // Record in Google Sheets
            if (this.sheetsService) {
                await this.sheetsService.recordInteraction(to, text, 'sent');
            }
        } catch (error) {
            this.logger.error('Failed to send message:', error.message);
        }
    }

    getCurrentQRCode() {
        return this.currentQRCode;
    }

    isConnectedAndReady() {
        return this.isConnected && this.isReady;
    }

    async forceRestart() {
        this.logger.info('🔄 Forcing complete bot restart...');

        // Reset all global state
        isGloballyInitializing = false;
        globalBotInstance = null;
        this.connectionAttempts = 0;
        this.isConnected = false;
        this.isReady = false;
        this.currentQR = null;

        // Complete cleanup
        await this.cleanup();

        // Force clear all auth directories
        const authPaths = [
            this.authDir,
            './auth_info_baileys',
            './baileys_auth_info',
            '/tmp/auth_info_baileys'
        ];

        for (const authPath of authPaths) {
            if (fs.existsSync(authPath)) {
                try {
                    fs.rmSync(authPath, { recursive: true, force: true });
                    this.logger.info(`🗑️ Cleared auth path: ${authPath}`);
                } catch (error) {
                    this.logger.warn(`⚠️ Could not clear ${authPath}: ${error.message}`);
                }
            }
        }

        // Wait longer before reinitializing
        this.logger.info('⏳ Waiting 5 seconds before fresh initialization...');
        setTimeout(() => {
            this.logger.info('🚀 Starting fresh initialization...');
            this.initialize();
        }, 5000);
    }
}
