import { Client, LocalAuth } from 'whatsapp-web.js';
import qrcode from 'qrcode-terminal';
import { Logger } from '../utils/logger.js';
import { Messages } from '../utils/messages.js';

export class WhatsAppBot {
    constructor(todoManager, aiProcessor) {
        this.todoManager = todoManager;
        this.aiProcessor = aiProcessor;
        this.logger = new Logger();
        this.client = null;
        this.isReady = false;
    }

    async initialize() {
        this.client = new Client({
            authStrategy: new LocalAuth(),
            puppeteer: {
                headless: true,
                args: [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-accelerated-2d-canvas',
                    '--no-first-run',
                    '--no-zygote',
                    '--single-process',
                    '--disable-gpu'
                ]
            }
        });

        this.setupEventHandlers();
        await this.client.initialize();
    }

    setupEventHandlers() {
        this.client.on('qr', (qr) => {
            this.logger.info('QR Code received, scan with your phone:');
            qrcode.generate(qr, { small: true });
        });

        this.client.on('ready', () => {
            this.logger.info('WhatsApp client is ready!');
            this.isReady = true;
        });

        this.client.on('authenticated', () => {
            this.logger.info('WhatsApp client authenticated');
        });

        this.client.on('auth_failure', (msg) => {
            this.logger.error('Authentication failed:', msg);
        });

        this.client.on('disconnected', (reason) => {
            this.logger.warn('WhatsApp client disconnected:', reason);
            this.isReady = false;
        });

        this.client.on('message', async (message) => {
            await this.handleMessage(message);
        });
    }

    async handleMessage(message) {
        try {
            // Ignore messages from groups and status updates
            if (message.from.includes('@g.us') || message.from.includes('status@broadcast')) {
                return;
            }

            // Ignore messages from the bot itself
            if (message.fromMe) {
                return;
            }

            const contact = await message.getContact();
            const userPhone = contact.number;
            const messageText = message.body.trim();

            this.logger.info(`Received message from ${contact.name || userPhone}: ${messageText}`);

            // Process the message with AI
            const response = await this.processUserMessage(userPhone, messageText);
            
            if (response) {
                await message.reply(response);
            }

        } catch (error) {
            this.logger.error('Error handling message:', error);
            await message.reply(Messages.ERROR.GENERAL);
        }
    }

    async processUserMessage(userPhone, messageText) {
        try {
            // Use AI to understand the user's intent
            const intent = await this.aiProcessor.processMessage(messageText);
            
            let response = '';

            switch (intent.action) {
                case 'add_todo':
                    response = await this.handleAddTodo(userPhone, intent);
                    break;
                
                case 'list_todos':
                    response = await this.handleListTodos(userPhone, intent);
                    break;
                
                case 'complete_todo':
                    response = await this.handleCompleteTodo(userPhone, intent);
                    break;
                
                case 'delete_todo':
                    response = await this.handleDeleteTodo(userPhone, intent);
                    break;
                
                case 'update_todo':
                    response = await this.handleUpdateTodo(userPhone, intent);
                    break;
                
                case 'help':
                    response = this.getHelpMessage();
                    break;

                case 'stats':
                    response = await this.handleStats(userPhone);
                    break;

                default:
                    response = await this.handleUnknownIntent(messageText);
                    break;
            }

            return response;

        } catch (error) {
            this.logger.error('Error processing user message:', error);
            return Messages.ERROR.UNDERSTANDING;
        }
    }

    async handleAddTodo(userPhone, intent) {
        const todo = await this.todoManager.addTodo(userPhone, intent.todoData);
        return Messages.formatTodoAdded(todo.title, todo.dueDate ? Messages.formatDueDate(todo.dueDate) : null);
    }

    async handleListTodos(userPhone, intent) {
        const todos = await this.todoManager.getTodos(userPhone, intent.filter);

        if (todos.length === 0) {
            if (intent.filter && Object.keys(intent.filter).length > 0) {
                return Messages.LISTS.NO_TODOS_FILTERED;
            }
            return Messages.LISTS.NO_TODOS_EMPTY;
        }

        let response = Messages.LISTS.YOUR_TODOS;
        const incompleteTodos = todos.filter(t => !t.completed);
        const completedTodos = todos.filter(t => t.completed);

        // Show incomplete todos first
        incompleteTodos.forEach((todo, index) => {
            const priority = this.getPriorityEmoji(todo.priority);
            const dueDate = todo.dueDate ? ` (${Messages.DATES.DUE}: ${Messages.formatDueDate(todo.dueDate)})` : '';
            const overdue = todo.dueDate && this.isOverdue(todo.dueDate) ? ' ⚠️' : '';
            response += `${index + 1}. ⏳ ${priority} ${todo.title}${dueDate}${overdue}\n`;
        });

        // Show completed todos if any
        if (completedTodos.length > 0 && !intent.filter?.completed === false) {
            response += Messages.LISTS.COMPLETED;
            completedTodos.forEach((todo, index) => {
                response += `${index + 1}. ✅ ${todo.title}\n`;
            });
        }

        // Add summary
        const stats = await this.todoManager.getUserStats(userPhone);
        response += Messages.formatListSummary(stats);

        return response;
    }

    async handleCompleteTodo(userPhone, intent) {
        const result = await this.todoManager.completeTodo(userPhone, intent.todoIdentifier);
        if (result.success) {
            return Messages.formatTodoCompleted(result.todo.title);
        } else {
            return Messages.formatTodoNotFound(intent.todoIdentifier);
        }
    }

    async handleDeleteTodo(userPhone, intent) {
        const result = await this.todoManager.deleteTodo(userPhone, intent.todoIdentifier);
        if (result.success) {
            return Messages.formatTodoDeleted(result.todo.title);
        } else {
            return Messages.formatTodoNotFound(intent.todoIdentifier);
        }
    }

    async handleUpdateTodo(userPhone, intent) {
        const result = await this.todoManager.updateTodo(userPhone, intent.todoIdentifier, intent.updates);
        if (result.success) {
            return Messages.formatTodoUpdated(result.todo.title);
        } else {
            return Messages.formatTodoNotFound(intent.todoIdentifier);
        }
    }

    getHelpMessage() {
        return Messages.getHelpMessage();
    }

    async handleStats(userPhone) {
        const stats = await this.todoManager.getUserStats(userPhone);
        return Messages.formatStats(stats);
    }

    async handleUnknownIntent(messageText) {
        return Messages.formatUnknownIntent(messageText);
    }

    getPriorityEmoji(priority) {
        const emojis = {
            'high': '🔴',
            'medium': '🟡',
            'low': '🟢'
        };
        return emojis[priority] || '🟡';
    }

    formatDueDate(dateString) {
        return Messages.formatDueDate(dateString);
    }

    isOverdue(dateString) {
        if (!dateString) return false;
        const date = new Date(dateString);
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        return date < today;
    }

    async destroy() {
        if (this.client) {
            await this.client.destroy();
        }
    }
}
