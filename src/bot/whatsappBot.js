import pkg from 'whatsapp-web.js';
const { Client, LocalAuth } = pkg;
import qrcode from 'qrcode-terminal';
import { Logger } from '../utils/logger.js';
import { Messages } from '../utils/messages.js';

export class WhatsAppBot {
    constructor(todoManager, aiProcessor, sheetsService, foundationManager, questManager) {
        this.todoManager = todoManager;
        this.aiProcessor = aiProcessor;
        this.sheetsService = sheetsService;
        this.foundationManager = foundationManager;
        this.questManager = questManager;
        this.logger = new Logger();
        this.client = null;
        this.isReady = false;
    }

    async initialize() {
        // Configuração otimizada para Cloud Run - tentativa final
        const puppeteerConfig = {
            headless: true,
            executablePath: '/usr/bin/chromium-browser',
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-gpu',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor',
                '--no-first-run',
                '--no-zygote',
                '--single-process',
                '--disable-extensions',
                '--disable-plugins',
                '--disable-default-apps',
                '--disable-sync',
                '--disable-background-timer-throttling',
                '--disable-backgrounding-occluded-windows',
                '--disable-renderer-backgrounding',
                '--memory-pressure-off',
                '--max_old_space_size=4096',
                '--disable-ipc-flooding-protection',
                '--disable-accelerated-2d-canvas',
                '--disable-accelerated-jpeg-decoding',
                '--disable-accelerated-mjpeg-decode',
                '--disable-accelerated-video-decode',
                '--disable-background-networking',
                '--disable-background-timer-throttling',
                '--disable-client-side-phishing-detection',
                '--disable-default-apps',
                '--disable-hang-monitor',
                '--disable-popup-blocking',
                '--disable-prompt-on-repost',
                '--disable-sync',
                '--disable-translate',
                '--metrics-recording-only',
                '--no-first-run',
                '--safebrowsing-disable-auto-update',
                '--enable-automation',
                '--password-store=basic',
                '--use-mock-keychain'
            ]
        };

        console.log('[' + new Date().toISOString() + '] [DEBUG] Creating WhatsApp client with config...');

        this.client = new Client({
            authStrategy: new LocalAuth(),
            puppeteer: puppeteerConfig
        });

        console.log('[' + new Date().toISOString() + '] [DEBUG] WhatsApp client created, setting up event handlers...');
        this.setupEventHandlers();

        console.log('[' + new Date().toISOString() + '] [DEBUG] Starting WhatsApp client initialization...');
        await this.client.initialize();
    }

    setupEventHandlers() {
        this.client.on('qr', (qr) => {
            this.logger.info('QR Code received, scan with your phone:');
            qrcode.generate(qr, { small: true });
        });

        this.client.on('ready', () => {
            this.logger.info('WhatsApp client is ready!');
            this.isReady = true;
        });

        this.client.on('authenticated', () => {
            this.logger.info('WhatsApp client authenticated');
        });

        this.client.on('auth_failure', (msg) => {
            this.logger.error('Authentication failed:', msg);
        });

        this.client.on('disconnected', (reason) => {
            this.logger.warn('WhatsApp client disconnected:', reason);
            this.isReady = false;
        });

        this.client.on('message', async (message) => {
            await this.handleMessage(message);
        });
    }

    async handleMessage(message) {
        const startTime = Date.now();
        let contact, userPhone, userName, messageText;

        try {
            // Ignore messages from groups and status updates
            if (message.from.includes('@g.us') || message.from.includes('status@broadcast')) {
                return;
            }

            // Ignore messages from the bot itself
            if (message.fromMe) {
                return;
            }

            contact = await message.getContact();
            userPhone = contact.number;
            userName = contact.name || contact.pushname || '';
            messageText = message.body.trim();

            this.logger.info(`Received message from ${userName || userPhone}: ${messageText}`);

            // Process the message with AI
            const response = await this.processUserMessage(userPhone, messageText, userName, startTime);

            if (response) {
                await message.reply(response);
            }

        } catch (error) {
            this.logger.error('Error handling message:', error);
            const errorResponse = Messages.ERROR.GENERAL;

            // Log the error interaction
            if (userPhone && messageText) {
                await this.logInteraction({
                    userPhone,
                    userName: userName || '',
                    messageText,
                    intentAction: 'error',
                    intentData: null,
                    responseText: errorResponse,
                    success: false,
                    errorMessage: error.message,
                    processingTime: Date.now() - startTime
                });
            }

            await message.reply(errorResponse);
        }
    }

    async processUserMessage(userPhone, messageText, userName, startTime) {
        let intent = null;
        let response = '';
        let success = true;
        let errorMessage = '';

        try {
            // Use AI to understand the user's intent
            intent = await this.aiProcessor.processMessage(messageText);

            switch (intent.action) {
                case 'add_todo':
                    response = await this.handleAddTodo(userPhone, intent);
                    break;

                case 'list_todos':
                    response = await this.handleListTodos(userPhone, intent);
                    break;

                case 'complete_todo':
                    response = await this.handleCompleteTodo(userPhone, intent);
                    break;

                case 'delete_todo':
                    response = await this.handleDeleteTodo(userPhone, intent);
                    break;

                case 'update_todo':
                    response = await this.handleUpdateTodo(userPhone, intent);
                    break;

                case 'help':
                    response = this.getHelpMessage();
                    break;

                case 'stats':
                    response = await this.handleStats(userPhone);
                    break;

                case 'add_foundation':
                    response = await this.handleAddFoundation(userPhone, intent);
                    break;

                case 'list_foundations':
                    response = await this.handleListFoundations(userPhone);
                    break;

                case 'update_foundation_score':
                    response = await this.handleUpdateFoundationScore(userPhone, intent);
                    break;

                case 'add_quest':
                    response = await this.handleAddQuest(userPhone, intent);
                    break;

                case 'list_quests':
                    response = await this.handleListQuests(userPhone);
                    break;

                case 'update_quest_progress':
                    response = await this.handleUpdateQuestProgress(userPhone, intent);
                    break;

                default:
                    response = await this.handleUnknownIntent(messageText);
                    break;
            }

        } catch (error) {
            this.logger.error('Error processing user message:', error);
            response = Messages.ERROR.UNDERSTANDING;
            success = false;
            errorMessage = error.message;
        }

        // Log the interaction
        await this.logInteraction({
            userPhone,
            userName: userName || '',
            messageText,
            intentAction: intent?.action || 'unknown',
            intentData: intent,
            responseText: response,
            success,
            errorMessage,
            processingTime: Date.now() - startTime
        });

        return response;
    }

    async handleAddTodo(userPhone, intent) {
        const todo = await this.todoManager.addTodo(userPhone, intent.todoData);
        return Messages.formatTodoAdded(todo.title, todo.dueDate ? Messages.formatDueDate(todo.dueDate) : null);
    }

    async handleListTodos(userPhone, intent) {
        const todos = await this.todoManager.getTodos(userPhone, intent.filter);

        if (todos.length === 0) {
            if (intent.filter && Object.keys(intent.filter).length > 0) {
                return Messages.LISTS.NO_TODOS_FILTERED;
            }
            return Messages.LISTS.NO_TODOS_EMPTY;
        }

        let response = Messages.LISTS.YOUR_TODOS;
        const incompleteTodos = todos.filter(t => !t.completed);
        const completedTodos = todos.filter(t => t.completed);

        // Show incomplete todos first
        incompleteTodos.forEach((todo, index) => {
            const priority = this.getPriorityEmoji(todo.priority);
            const dueDate = todo.dueDate ? ` (${Messages.DATES.DUE}: ${Messages.formatDueDate(todo.dueDate)})` : '';
            const overdue = todo.dueDate && this.isOverdue(todo.dueDate) ? ' ⚠️' : '';
            response += `${index + 1}. ⏳ ${priority} ${todo.title}${dueDate}${overdue}\n`;
        });

        // Show completed todos if any
        if (completedTodos.length > 0 && !intent.filter?.completed === false) {
            response += Messages.LISTS.COMPLETED;
            completedTodos.forEach((todo, index) => {
                response += `${index + 1}. ✅ ${todo.title}\n`;
            });
        }

        // Add summary
        const stats = await this.todoManager.getUserStats(userPhone);
        response += Messages.formatListSummary(stats);

        return response;
    }

    async handleCompleteTodo(userPhone, intent) {
        const result = await this.todoManager.completeTodo(userPhone, intent.todoIdentifier);
        if (result.success) {
            return Messages.formatTodoCompleted(result.todo.title);
        } else {
            return Messages.formatTodoNotFound(intent.todoIdentifier);
        }
    }

    async handleDeleteTodo(userPhone, intent) {
        const result = await this.todoManager.deleteTodo(userPhone, intent.todoIdentifier);
        if (result.success) {
            return Messages.formatTodoDeleted(result.todo.title);
        } else {
            return Messages.formatTodoNotFound(intent.todoIdentifier);
        }
    }

    async handleUpdateTodo(userPhone, intent) {
        const result = await this.todoManager.updateTodo(userPhone, intent.todoIdentifier, intent.updates);
        if (result.success) {
            return Messages.formatTodoUpdated(result.todo.title);
        } else {
            return Messages.formatTodoNotFound(intent.todoIdentifier);
        }
    }

    getHelpMessage() {
        return Messages.getHelpMessage();
    }

    async handleStats(userPhone) {
        const stats = await this.todoManager.getUserStats(userPhone);
        return Messages.formatStats(stats);
    }

    async handleUnknownIntent(messageText) {
        return Messages.formatUnknownIntent(messageText);
    }

    // Foundation handlers
    async handleAddFoundation(userPhone, intent) {
        try {
            const foundation = await this.foundationManager.addFoundation(userPhone, intent.foundationData);
            return Messages.formatFoundationAdded(foundation.name);
        } catch (error) {
            if (error.message.includes('already exists')) {
                return Messages.FOUNDATIONS.DUPLICATE_FOUNDATION.replace('{name}', intent.foundationData.name);
            } else if (error.message.includes('Maximum')) {
                return Messages.FOUNDATIONS.MAX_FOUNDATIONS;
            } else {
                return Messages.ERROR.GENERAL;
            }
        }
    }

    async handleListFoundations(userPhone) {
        try {
            const foundations = await this.foundationManager.getFoundations(userPhone);
            return this.foundationManager.formatFoundationsList(foundations);
        } catch (error) {
            this.logger.error('Error listing foundations:', error);
            return Messages.ERROR.GENERAL;
        }
    }

    async handleUpdateFoundationScore(userPhone, intent) {
        const result = await this.foundationManager.updateFoundationScore(
            userPhone,
            intent.foundationIdentifier,
            intent.score
        );

        if (result.success) {
            return Messages.formatFoundationScoreUpdated(
                result.foundation.name,
                result.foundation.currentScore,
                result.foundation.targetScore
            );
        } else {
            return Messages.formatFoundationNotFound(intent.foundationIdentifier);
        }
    }

    // Quest handlers
    async handleAddQuest(userPhone, intent) {
        try {
            const quest = await this.questManager.addQuest(userPhone, intent.questData);
            return Messages.formatQuestAdded(quest.title, this.formatDueDate(quest.dueDate));
        } catch (error) {
            if (error.message.includes('already exists')) {
                return Messages.QUESTS.DUPLICATE_QUEST.replace('{title}', intent.questData.title);
            } else {
                return Messages.ERROR.GENERAL;
            }
        }
    }

    async handleListQuests(userPhone) {
        try {
            const quests = await this.questManager.getQuests(userPhone);
            return this.questManager.formatQuestsList(quests);
        } catch (error) {
            this.logger.error('Error listing quests:', error);
            return Messages.ERROR.GENERAL;
        }
    }

    async handleUpdateQuestProgress(userPhone, intent) {
        const result = await this.questManager.updateQuestProgress(
            userPhone,
            intent.questIdentifier,
            intent.progress
        );

        if (result.success) {
            if (result.quest.status === 'completed') {
                return Messages.formatQuestCompleted(result.quest.title);
            } else {
                return Messages.formatQuestProgressUpdated(result.quest.title, result.quest.progressPercentage);
            }
        } else {
            return Messages.formatQuestNotFound(intent.questIdentifier);
        }
    }

    getPriorityEmoji(priority) {
        const emojis = {
            'high': '🔴',
            'medium': '🟡',
            'low': '🟢'
        };
        return emojis[priority] || '🟡';
    }

    formatDueDate(dateString) {
        return Messages.formatDueDate(dateString);
    }

    isOverdue(dateString) {
        if (!dateString) return false;
        const date = new Date(dateString);
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        return date < today;
    }

    async logInteraction(interactionData) {
        try {
            if (this.sheetsService) {
                await this.sheetsService.logInteraction(interactionData);
            }
        } catch (error) {
            this.logger.error('Error logging interaction:', error);
            // Don't throw error to avoid disrupting the main flow
        }
    }

    async destroy() {
        if (this.client) {
            await this.client.destroy();
        }
    }
}
