import { createRequire } from 'module';
const require = createRequire(import.meta.url);
const { default: makeWASocket, DisconnectReason, useMultiFileAuthState, fetchLatestBaileysVersion } = require('@whiskeysockets/baileys');
import { Logger } from '../utils/logger.js';
import { Messages } from '../utils/messages.js';
import fs from 'fs';

export class WhatsAppBot {
    constructor(todoManager, aiProcessor, sheetsService, foundationManager, questManager) {
        this.todoManager = todoManager;
        this.aiProcessor = aiProcessor;
        this.sheetsService = sheetsService;
        this.foundationManager = foundationManager;
        this.questManager = questManager;
        this.logger = new Logger();
        this.sock = null;
        this.isReady = false;
        this.authDir = './baileys_auth';
        this.currentQRCode = null; // Store current QR code data
    }

    async initialize() {
        try {
            this.logger.info('Initializing Baileys WhatsApp connection...');

            // Clear any existing socket first
            if (this.sock) {
                try {
                    this.sock.end();
                } catch (e) {
                    // Ignore errors when ending existing socket
                }
                this.sock = null;
            }

            // Create auth directory if it doesn't exist
            if (!fs.existsSync(this.authDir)) {
                fs.mkdirSync(this.authDir, { recursive: true });
            }

            // Get auth state with error handling for corruption
            let state, saveCreds;
            try {
                ({ state, saveCreds } = await useMultiFileAuthState(this.authDir));
            } catch (authError) {
                this.logger.warn('Auth state error, clearing and retrying:', authError.message);

                // Clear corrupted auth state
                if (fs.existsSync(this.authDir)) {
                    fs.rmSync(this.authDir, { recursive: true, force: true });
                    this.logger.info('Cleared corrupted auth state');

                    // Recreate directory
                    fs.mkdirSync(this.authDir, { recursive: true });
                }

                // Retry with fresh auth state
                ({ state, saveCreds } = await useMultiFileAuthState(this.authDir));
            }

            // Get latest Baileys version
            const { version, isLatest } = await fetchLatestBaileysVersion();
            this.logger.info(`Using Baileys version ${version}, isLatest: ${isLatest}`);

            // Create socket
            this.sock = makeWASocket({
                version,
                auth: state,
                printQRInTerminal: false, // We'll handle QR manually
                logger: {
                    level: 'silent', // Disable Baileys internal logging to prevent errors
                    child: () => ({
                        level: 'silent',
                        trace: () => {},
                        debug: () => {},
                        info: () => {},
                        warn: () => {},
                        error: () => {},
                        fatal: () => {}
                    }),
                    trace: () => {},
                    debug: () => {},
                    info: () => {},
                    warn: () => {},
                    error: () => {},
                    fatal: () => {}
                }
            });

            // Setup event handlers
            this.setupEventHandlers(saveCreds);

            this.logger.info('Baileys WhatsApp socket created successfully');

        } catch (error) {
            this.logger.error('Failed to initialize Baileys WhatsApp:', error);
            throw error;
        }
    }

    setupEventHandlers(saveCreds) {
        // Connection updates (including QR code)
        this.sock.ev.on('connection.update', (update) => {
            const { connection, lastDisconnect, qr } = update;

            if (qr) {
                // Store QR code data for web display
                this.currentQRCode = qr;

                this.logger.info('📱 QR Code received! Visit the web interface to scan:');
                this.logger.info('🌐 QR Code URL: https://fococriador-6a2gqq6k7a-uc.a.run.app/qr-image');
                console.log('📱 QR Code received! Visit the web interface to scan:');
                console.log('🌐 QR Code URL: https://fococriador-6a2gqq6k7a-uc.a.run.app/qr-image');
                console.log('='.repeat(70));

                // Also log the raw QR data for debugging
                this.logger.info('Raw QR data:', qr);
            }

            if (connection === 'close') {
                const shouldReconnect = (lastDisconnect?.error)?.output?.statusCode !== DisconnectReason.loggedOut;
                this.logger.info('Connection closed due to:', lastDisconnect?.error, ', reconnecting:', shouldReconnect);

                if (shouldReconnect) {
                    this.initialize(); // Reconnect
                }
                this.isReady = false;
            } else if (connection === 'open') {
                this.logger.info('WhatsApp connection opened successfully!');
                this.isReady = true;
            }
        });

        // Save credentials when updated
        this.sock.ev.on('creds.update', saveCreds);

        // Handle incoming messages
        this.sock.ev.on('messages.upsert', async (m) => {
            const messages = m.messages;
            for (const message of messages) {
                if (!message.key.fromMe && message.message) {
                    await this.handleMessage(message);
                }
            }
        });
    }

    async handleMessage(message) {
        const startTime = Date.now();
        let userPhone, userName, messageText;

        try {
            // Extract message info from Baileys format
            const remoteJid = message.key.remoteJid;

            // Ignore messages from groups and status updates
            if (remoteJid.includes('@g.us') || remoteJid.includes('status@broadcast')) {
                return;
            }

            // Extract phone number (remove @s.whatsapp.net)
            userPhone = remoteJid.replace('@s.whatsapp.net', '');

            // Extract message text
            messageText = message.message?.conversation ||
                         message.message?.extendedTextMessage?.text ||
                         '';

            if (!messageText.trim()) {
                return; // Ignore empty messages
            }

            messageText = messageText.trim();

            // Extract user name (if available)
            userName = message.pushName || '';

            this.logger.info(`Received message from ${userName || userPhone}: ${messageText}`);

            // Process the message with AI
            const response = await this.processUserMessage(userPhone, messageText, userName, startTime);

            if (response) {
                await this.sendMessage(remoteJid, response);
            }

        } catch (error) {
            this.logger.error('Error handling message:', error);
            const errorResponse = Messages.ERROR.GENERAL;

            // Log the error interaction
            if (userPhone && messageText) {
                await this.logInteraction({
                    userPhone,
                    userName: userName || '',
                    messageText,
                    intentAction: 'error',
                    intentData: null,
                    responseText: errorResponse,
                    success: false,
                    errorMessage: error.message,
                    processingTime: Date.now() - startTime
                });
            }

            if (message.key.remoteJid) {
                await this.sendMessage(message.key.remoteJid, errorResponse);
            }
        }
    }

    async sendMessage(jid, text) {
        try {
            await this.sock.sendMessage(jid, { text });
        } catch (error) {
            this.logger.error('Error sending message:', error);
            throw error;
        }
    }

    async processUserMessage(userPhone, messageText, userName, startTime) {
        let intent = null;
        let response = '';
        let success = true;
        let errorMessage = '';

        try {
            // Use AI to understand the user's intent
            intent = await this.aiProcessor.processMessage(messageText);

            switch (intent.action) {
                case 'add_todo':
                    response = await this.handleAddTodo(userPhone, intent);
                    break;

                case 'list_todos':
                    response = await this.handleListTodos(userPhone, intent);
                    break;

                case 'complete_todo':
                    response = await this.handleCompleteTodo(userPhone, intent);
                    break;

                case 'delete_todo':
                    response = await this.handleDeleteTodo(userPhone, intent);
                    break;

                case 'update_todo':
                    response = await this.handleUpdateTodo(userPhone, intent);
                    break;

                case 'help':
                    response = this.getHelpMessage();
                    break;

                case 'stats':
                    response = await this.handleStats(userPhone);
                    break;

                case 'add_foundation':
                    response = await this.handleAddFoundation(userPhone, intent);
                    break;

                case 'list_foundations':
                    response = await this.handleListFoundations(userPhone);
                    break;

                case 'update_foundation_score':
                    response = await this.handleUpdateFoundationScore(userPhone, intent);
                    break;

                case 'add_quest':
                    response = await this.handleAddQuest(userPhone, intent);
                    break;

                case 'list_quests':
                    response = await this.handleListQuests(userPhone);
                    break;

                case 'update_quest_progress':
                    response = await this.handleUpdateQuestProgress(userPhone, intent);
                    break;

                default:
                    response = await this.handleUnknownIntent(messageText);
                    break;
            }

        } catch (error) {
            this.logger.error('Error processing user message:', error);
            response = Messages.ERROR.UNDERSTANDING;
            success = false;
            errorMessage = error.message;
        }

        // Log the interaction
        await this.logInteraction({
            userPhone,
            userName: userName || '',
            messageText,
            intentAction: intent?.action || 'unknown',
            intentData: intent,
            responseText: response,
            success,
            errorMessage,
            processingTime: Date.now() - startTime
        });

        return response;
    }

    async handleAddTodo(userPhone, intent) {
        const todo = await this.todoManager.addTodo(userPhone, intent.todoData);
        return Messages.formatTodoAdded(todo.title, todo.dueDate ? Messages.formatDueDate(todo.dueDate) : null);
    }

    async handleListTodos(userPhone, intent) {
        const todos = await this.todoManager.getTodos(userPhone, intent.filter);

        if (todos.length === 0) {
            if (intent.filter && Object.keys(intent.filter).length > 0) {
                return Messages.LISTS.NO_TODOS_FILTERED;
            }
            return Messages.LISTS.NO_TODOS_EMPTY;
        }

        let response = Messages.LISTS.YOUR_TODOS;
        const incompleteTodos = todos.filter(t => !t.completed);
        const completedTodos = todos.filter(t => t.completed);

        // Show incomplete todos first
        incompleteTodos.forEach((todo, index) => {
            const priority = this.getPriorityEmoji(todo.priority);
            const dueDate = todo.dueDate ? ` (${Messages.DATES.DUE}: ${Messages.formatDueDate(todo.dueDate)})` : '';
            const overdue = todo.dueDate && this.isOverdue(todo.dueDate) ? ' ⚠️' : '';
            response += `${index + 1}. ⏳ ${priority} ${todo.title}${dueDate}${overdue}\n`;
        });

        // Show completed todos if any
        if (completedTodos.length > 0 && !intent.filter?.completed === false) {
            response += Messages.LISTS.COMPLETED;
            completedTodos.forEach((todo, index) => {
                response += `${index + 1}. ✅ ${todo.title}\n`;
            });
        }

        // Add summary
        const stats = await this.todoManager.getUserStats(userPhone);
        response += Messages.formatListSummary(stats);

        return response;
    }

    async handleCompleteTodo(userPhone, intent) {
        const result = await this.todoManager.completeTodo(userPhone, intent.todoIdentifier);
        if (result.success) {
            return Messages.formatTodoCompleted(result.todo.title);
        } else {
            return Messages.formatTodoNotFound(intent.todoIdentifier);
        }
    }

    async handleDeleteTodo(userPhone, intent) {
        const result = await this.todoManager.deleteTodo(userPhone, intent.todoIdentifier);
        if (result.success) {
            return Messages.formatTodoDeleted(result.todo.title);
        } else {
            return Messages.formatTodoNotFound(intent.todoIdentifier);
        }
    }

    async handleUpdateTodo(userPhone, intent) {
        const result = await this.todoManager.updateTodo(userPhone, intent.todoIdentifier, intent.updates);
        if (result.success) {
            return Messages.formatTodoUpdated(result.todo.title);
        } else {
            return Messages.formatTodoNotFound(intent.todoIdentifier);
        }
    }

    getHelpMessage() {
        return Messages.getHelpMessage();
    }

    async handleStats(userPhone) {
        const stats = await this.todoManager.getUserStats(userPhone);
        return Messages.formatStats(stats);
    }

    async handleUnknownIntent(messageText) {
        return Messages.formatUnknownIntent(messageText);
    }

    // Foundation handlers
    async handleAddFoundation(userPhone, intent) {
        try {
            const foundation = await this.foundationManager.addFoundation(userPhone, intent.foundationData);
            return Messages.formatFoundationAdded(foundation.name);
        } catch (error) {
            if (error.message.includes('already exists')) {
                return Messages.FOUNDATIONS.DUPLICATE_FOUNDATION.replace('{name}', intent.foundationData.name);
            } else if (error.message.includes('Maximum')) {
                return Messages.FOUNDATIONS.MAX_FOUNDATIONS;
            } else {
                return Messages.ERROR.GENERAL;
            }
        }
    }

    async handleListFoundations(userPhone) {
        try {
            const foundations = await this.foundationManager.getFoundations(userPhone);
            return this.foundationManager.formatFoundationsList(foundations);
        } catch (error) {
            this.logger.error('Error listing foundations:', error);
            return Messages.ERROR.GENERAL;
        }
    }

    async handleUpdateFoundationScore(userPhone, intent) {
        const result = await this.foundationManager.updateFoundationScore(
            userPhone,
            intent.foundationIdentifier,
            intent.score
        );

        if (result.success) {
            return Messages.formatFoundationScoreUpdated(
                result.foundation.name,
                result.foundation.currentScore,
                result.foundation.targetScore
            );
        } else {
            return Messages.formatFoundationNotFound(intent.foundationIdentifier);
        }
    }

    // Quest handlers
    async handleAddQuest(userPhone, intent) {
        try {
            const quest = await this.questManager.addQuest(userPhone, intent.questData);
            return Messages.formatQuestAdded(quest.title, this.formatDueDate(quest.dueDate));
        } catch (error) {
            if (error.message.includes('already exists')) {
                return Messages.QUESTS.DUPLICATE_QUEST.replace('{title}', intent.questData.title);
            } else {
                return Messages.ERROR.GENERAL;
            }
        }
    }

    async handleListQuests(userPhone) {
        try {
            const quests = await this.questManager.getQuests(userPhone);
            return this.questManager.formatQuestsList(quests);
        } catch (error) {
            this.logger.error('Error listing quests:', error);
            return Messages.ERROR.GENERAL;
        }
    }

    async handleUpdateQuestProgress(userPhone, intent) {
        const result = await this.questManager.updateQuestProgress(
            userPhone,
            intent.questIdentifier,
            intent.progress
        );

        if (result.success) {
            if (result.quest.status === 'completed') {
                return Messages.formatQuestCompleted(result.quest.title);
            } else {
                return Messages.formatQuestProgressUpdated(result.quest.title, result.quest.progressPercentage);
            }
        } else {
            return Messages.formatQuestNotFound(intent.questIdentifier);
        }
    }

    getPriorityEmoji(priority) {
        const emojis = {
            'high': '🔴',
            'medium': '🟡',
            'low': '🟢'
        };
        return emojis[priority] || '🟡';
    }

    formatDueDate(dateString) {
        return Messages.formatDueDate(dateString);
    }

    isOverdue(dateString) {
        if (!dateString) return false;
        const date = new Date(dateString);
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        return date < today;
    }

    async logInteraction(interactionData) {
        try {
            if (this.sheetsService) {
                await this.sheetsService.logInteraction(interactionData);
            }
        } catch (error) {
            this.logger.error('Error logging interaction:', error);
            // Don't throw error to avoid disrupting the main flow
        }
    }

    async destroy() {
        if (this.client) {
            await this.client.destroy();
        }
    }

    getCurrentQRCode() {
        return this.currentQRCode;
    }
}
