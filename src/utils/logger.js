class Logger {
    constructor() {
        this.logLevel = process.env.LOG_LEVEL || 'info';
        this.debug = process.env.DEBUG === 'true';
        
        this.levels = {
            error: 0,
            warn: 1,
            info: 2,
            debug: 3
        };
    }

    shouldLog(level) {
        return this.levels[level] <= this.levels[this.logLevel];
    }

    formatMessage(level, message, ...args) {
        const timestamp = new Date().toISOString();
        const prefix = `[${timestamp}] [${level.toUpperCase()}]`;
        
        if (args.length > 0) {
            return `${prefix} ${message}`;
        }
        return `${prefix} ${message}`;
    }

    error(message, ...args) {
        if (this.shouldLog('error')) {
            console.error(this.formatMessage('error', message), ...args);
        }
    }

    warn(message, ...args) {
        if (this.shouldLog('warn')) {
            console.warn(this.formatMessage('warn', message), ...args);
        }
    }

    info(message, ...args) {
        if (this.shouldLog('info')) {
            console.log(this.formatMessage('info', message), ...args);
        }
    }

    debug(message, ...args) {
        if (this.shouldLog('debug') || this.debug) {
            console.log(this.formatMessage('debug', message), ...args);
        }
    }
}


module.exports = { Logger };