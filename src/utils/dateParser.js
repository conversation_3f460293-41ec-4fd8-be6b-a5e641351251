const moment = require('moment');
const { Messages } = require('./messages.js');

class DateParser {
    constructor() {
        this.timezone = process.env.DEFAULT_TIMEZONE || 'America/New_York';
    }

    parseDate(dateString) {
        if (!dateString) return null;

        // If it's already in YYYY-MM-DD format, return as is
        if (/^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
            return dateString;
        }

        const date = this.parseNaturalLanguageDate(dateString);
        return date ? date.format('YYYY-MM-DD') : null;
    }

    extractDateFromText(text) {
        const datePatterns = [
            // Portuguese relative dates
            { pattern: /\b(hoje|hj)\b/i, handler: () => moment() },
            { pattern: /\b(amanhã|amanha)\b/i, handler: () => moment().add(1, 'day') },
            { pattern: /\b(ontem)\b/i, handler: () => moment().subtract(1, 'day') },

            // English relative dates (fallback)
            { pattern: /\b(today|tonight)\b/i, handler: () => moment() },
            { pattern: /\b(tomorrow|tmrw)\b/i, handler: () => moment().add(1, 'day') },
            { pattern: /\b(yesterday)\b/i, handler: () => moment().subtract(1, 'day') },

            // Portuguese days of week
            { pattern: /\b(próxim[ao]\s+)?(segunda|terça|quarta|quinta|sexta|sábado|domingo)\b/i,
              handler: (match) => this.getNextWeekdayPortuguese(match[2], match[1] ? true : false) },

            // English days of week (fallback)
            { pattern: /\b(next\s+)?(monday|tuesday|wednesday|thursday|friday|saturday|sunday)\b/i,
              handler: (match) => this.getNextWeekday(match[2], match[1] ? true : false) },

            // Portuguese "em X dias/semanas/meses"
            { pattern: /\b(em|daqui)\s+(\d+)\s+(dia|dias|semana|semanas|mês|meses)\b/i,
              handler: (match) => moment().add(parseInt(match[2]), this.translateTimeUnit(match[3])) },

            // English "in X days/weeks/months" (fallback)
            { pattern: /\bin\s+(\d+)\s+(day|days|week|weeks|month|months)\b/i,
              handler: (match) => moment().add(parseInt(match[1]), match[2].replace('s', '')) },

            // Portuguese "X dias/semanas/meses"
            { pattern: /\b(\d+)\s+(dia|dias|semana|semanas|mês|meses)\b/i,
              handler: (match) => moment().add(parseInt(match[1]), this.translateTimeUnit(match[2])) },

            // X days/weeks/months from now (English fallback)
            { pattern: /\b(\d+)\s+(day|days|week|weeks|month|months)\s+from\s+now\b/i,
              handler: (match) => moment().add(parseInt(match[1]), match[2].replace('s', '')) },

            // Specific dates
            { pattern: /\b(\d{1,2})\/(\d{1,2})(?:\/(\d{2,4}))?\b/,
              handler: (match) => this.parseSlashDate(match[1], match[2], match[3]) },

            { pattern: /\b(\d{1,2})-(\d{1,2})(?:-(\d{2,4}))?\b/,
              handler: (match) => this.parseSlashDate(match[1], match[2], match[3]) },

            // Portuguese month day format
            { pattern: /\b(janeiro|fevereiro|março|abril|maio|junho|julho|agosto|setembro|outubro|novembro|dezembro)\s+(\d{1,2})\b/i,
              handler: (match) => this.parsePortugueseMonth(match[1], match[2]) },

            // English month day format (fallback)
            { pattern: /\b(january|february|march|april|may|june|july|august|september|october|november|december)\s+(\d{1,2})(?:st|nd|rd|th)?\b/i,
              handler: (match) => moment(`${match[1]} ${match[2]}`, 'MMMM DD') },

            // Portuguese "até/para" dates
            { pattern: /\b(até|para|antes de)\s+(.*?)(?:\s|$)/i,
              handler: (match) => this.parseNaturalLanguageDate(match[2]) },

            // English "by/before" dates (fallback)
            { pattern: /\b(by|before)\s+(.*?)(?:\s|$)/i,
              handler: (match) => this.parseNaturalLanguageDate(match[2]) }
        ];

        for (const { pattern, handler } of datePatterns) {
            const match = text.match(pattern);
            if (match) {
                const date = handler(match);
                if (date && date.isValid()) {
                    return {
                        date: date.format('YYYY-MM-DD'),
                        dateText: match[0]
                    };
                }
            }
        }

        return { date: null, dateText: null };
    }

    translateTimeUnit(unit) {
        const translations = {
            'dia': 'day',
            'dias': 'days',
            'semana': 'week',
            'semanas': 'weeks',
            'mês': 'month',
            'meses': 'months'
        };
        return translations[unit.toLowerCase()] || unit;
    }

    parsePortugueseMonth(monthName, day) {
        const monthMap = {
            'janeiro': 'January',
            'fevereiro': 'February',
            'março': 'March',
            'abril': 'April',
            'maio': 'May',
            'junho': 'June',
            'julho': 'July',
            'agosto': 'August',
            'setembro': 'September',
            'outubro': 'October',
            'novembro': 'November',
            'dezembro': 'December'
        };

        const englishMonth = monthMap[monthName.toLowerCase()];
        if (englishMonth) {
            return moment(`${englishMonth} ${day}`, 'MMMM DD');
        }
        return null;
    }

    getNextWeekdayPortuguese(dayName, forceNext = false) {
        const days = {
            'segunda': 1, 'terça': 2, 'quarta': 3, 'quinta': 4,
            'sexta': 5, 'sábado': 6, 'domingo': 0
        };

        const targetDay = days[dayName.toLowerCase()];
        if (targetDay === undefined) return null;

        const today = moment();
        const currentDay = today.day();

        let daysToAdd = targetDay - currentDay;

        // If the target day is today or has passed this week, go to next week
        if (daysToAdd <= 0 || forceNext) {
            daysToAdd += 7;
        }

        return today.add(daysToAdd, 'days');
    }

    parseNaturalLanguageDate(dateString) {
        const text = dateString.toLowerCase().trim();

        // Handle Portuguese relative dates
        if (text === 'hoje' || text === 'hj') {
            return moment();
        }
        if (text === 'amanhã' || text === 'amanha') {
            return moment().add(1, 'day');
        }
        if (text === 'ontem') {
            return moment().subtract(1, 'day');
        }

        // Handle English relative dates (fallback)
        if (text === 'today' || text === 'tonight') {
            return moment();
        }
        if (text === 'tomorrow' || text === 'tmrw') {
            return moment().add(1, 'day');
        }
        if (text === 'yesterday') {
            return moment().subtract(1, 'day');
        }

        // Handle Portuguese "próxima semana", "esta semana"
        if (text === 'próxima semana') {
            return moment().add(1, 'week').startOf('week');
        }
        if (text === 'esta semana') {
            return moment().startOf('week');
        }

        // Handle English "next week", "this week" (fallback)
        if (text === 'next week') {
            return moment().add(1, 'week').startOf('week');
        }
        if (text === 'this week') {
            return moment().startOf('week');
        }

        // Handle Portuguese weekdays
        const portugueseWeekdays = ['segunda', 'terça', 'quarta', 'quinta', 'sexta', 'sábado', 'domingo'];
        for (const day of portugueseWeekdays) {
            if (text.includes(day)) {
                const isNext = text.includes('próxim');
                return this.getNextWeekdayPortuguese(day, isNext);
            }
        }

        // Handle English weekdays (fallback)
        const weekdays = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
        for (const day of weekdays) {
            if (text.includes(day)) {
                const isNext = text.includes('next');
                return this.getNextWeekday(day, isNext);
            }
        }

        // Try parsing with moment
        const formats = [
            'YYYY-MM-DD',
            'MM/DD/YYYY',
            'MM/DD/YY',
            'MM/DD',
            'MMMM DD, YYYY',
            'MMMM DD',
            'MMM DD',
            'DD/MM/YYYY',
            'DD/MM/YY'
        ];

        for (const format of formats) {
            const parsed = moment(dateString, format, true);
            if (parsed.isValid()) {
                return parsed;
            }
        }

        return null;
    }

    getNextWeekday(dayName, forceNext = false) {
        const days = {
            'monday': 1, 'tuesday': 2, 'wednesday': 3, 'thursday': 4,
            'friday': 5, 'saturday': 6, 'sunday': 0
        };

        const targetDay = days[dayName.toLowerCase()];
        if (targetDay === undefined) return null;

        const today = moment();
        const currentDay = today.day();
        
        let daysToAdd = targetDay - currentDay;
        
        // If the target day is today or has passed this week, go to next week
        if (daysToAdd <= 0 || forceNext) {
            daysToAdd += 7;
        }

        return today.add(daysToAdd, 'days');
    }

    parseSlashDate(month, day, year) {
        const currentYear = moment().year();
        const parsedYear = year ? (year.length === 2 ? 2000 + parseInt(year) : parseInt(year)) : currentYear;
        
        const date = moment(`${parsedYear}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`, 'YYYY-MM-DD');
        
        // If the date is in the past and no year was specified, assume next year
        if (!year && date.isBefore(moment(), 'day')) {
            date.add(1, 'year');
        }
        
        return date.isValid() ? date : null;
    }

    isOverdue(dateString) {
        if (!dateString) return false;
        const date = moment(dateString);
        return date.isValid() && date.isBefore(moment(), 'day');
    }

    isToday(dateString) {
        if (!dateString) return false;
        const date = moment(dateString);
        return date.isValid() && date.isSame(moment(), 'day');
    }

    formatDate(dateString) {
        if (!dateString) return '';
        const date = moment(dateString);
        if (!date.isValid()) return dateString;

        if (date.isSame(moment(), 'day')) {
            return 'Today';
        }
        if (date.isSame(moment().add(1, 'day'), 'day')) {
            return 'Tomorrow';
        }
        if (date.isSame(moment().subtract(1, 'day'), 'day')) {
            return 'Yesterday';
        }

        return date.format('MMM DD, YYYY');
    }
}


module.exports = { DateParser };