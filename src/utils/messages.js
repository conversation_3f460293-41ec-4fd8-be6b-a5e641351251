/**
 * Portuguese messages for the WhatsApp Todo Bot
 * All user-facing text in Portuguese
 */

export class Messages {
    static ERROR = {
        GENERAL: '<PERSON><PERSON><PERSON><PERSON>, encontrei um erro ao processar sua mensagem. Tente novamente.',
        UNDERSTANDING: '<PERSON><PERSON><PERSON><PERSON>, tive dificuldade para entender sua mensagem. Tente pedir ajuda digitando "ajuda".',
        TODO_NOT_FOUND: 'Não consegui encontrar a tarefa:',
        EMPTY_TITLE: 'O título da tarefa não pode estar vazio',
        MAX_TODOS_REACHED: 'Você atingiu o limite máximo de',
        UNKNOWN_INTENT: '🤔 Não tenho certeza do que você quer fazer. Tente ser mais específico ou digite "ajuda" para ver exemplos.\n\nSua mensagem:'
    };

    static SUCCESS = {
        TODO_ADDED: '✅ Tarefa adicionada:',
        TODO_COMPLETED: '✅ Marcou como concluída:',
        TODO_DELETED: '🗑️ Tarefa excluída:',
        TODO_UPDATED: '✏️ Tarefa atualizada:'
    };

    static LISTS = {
        NO_TODOS_FILTERED: '📝 Nenhuma tarefa encontrada com seus critérios. Tente "listar minhas tarefas" para ver todas.',
        NO_TODOS_EMPTY: '📝 Você não tem tarefas. Adicione uma dizendo algo como "adicionar comprar mantimentos amanhã"',
        YOUR_TODOS: '📝 Suas tarefas:\n\n',
        COMPLETED: '\n*Concluídas:*\n',
        SUMMARY: '\n📊 *Resumo:*',
        PENDING: 'pendentes',
        COMPLETED_COUNT: 'concluídas',
        OVERDUE: 'atrasadas'
    };

    static DATES = {
        TODAY: 'Hoje',
        TOMORROW: 'Amanhã',
        DUE: 'Vence'
    };

    static STATS = {
        TITLE: '📊 *Suas Estatísticas de Tarefas*\n\n',
        TOTAL: '📝 Total de tarefas:',
        PENDING: '⏳ Pendentes:',
        COMPLETED: '✅ Concluídas:',
        OVERDUE: '⚠️ Atrasadas:',
        DUE_TODAY: '📅 Vencem hoje:',
        COMPLETION_RATE: '\n🎯 Taxa de conclusão:'
    };

    static HELP = {
        TITLE: '🎯 *Foco Criador - Seu Assistente de Produtividade*\n\n',
        INTRO: 'Transformo suas ideias em realidade através de foco e organização! Aqui estão alguns exemplos:\n\n',
        ADDING: '*Adicionando missões:*\n',
        ADDING_EXAMPLES: [
            '• "adicionar comprar mantimentos amanhã"',
            '• "me lembrar de ligar para a mãe até sexta"',
            '• "adicionar reunião com equipe às 15h"',
            '• "adicionar urgente: corrigir o bug"'
        ],
        VIEWING: '\n*Visualizando missões:*\n',
        VIEWING_EXAMPLES: [
            '• "listar minhas missões"',
            '• "mostrar missões atrasadas"',
            '• "o que vence hoje?"',
            '• "mostrar missões concluídas"'
        ],
        COMPLETING: '\n*Concluindo missões:*\n',
        COMPLETING_EXAMPLES: [
            '• "marcar comprar mantimentos como feito"',
            '• "concluir a primeira missão"',
            '• "feito com mantimentos"'
        ],
        DELETING: '\n*Excluindo tarefas:*\n',
        DELETING_EXAMPLES: [
            '• "excluir tarefa de mantimentos"',
            '• "remover a tarefa da reunião"'
        ],
        OTHER: '\n*Outros comandos:*\n',
        OTHER_EXAMPLES: [
            '• "estatísticas" - ver suas estatísticas de tarefas',
            '• "ajuda" - mostrar esta mensagem de ajuda'
        ],
        FOOTER: '\n\n💡 *Dica:* Seja natural! Entendo linguagem coloquial.\n\n🎯 *Fundamentos e Objetivos:*\n• "criar fundamento saúde"\n• "objetivo: ler 12 livros em 90 dias"\n• "listar meus fundamentos"\n• "mostrar objetivos ativos"\n\n🚀 *Vamos focar no que realmente importa!*\n\nSuas missões são automaticamente sincronizadas com o Google Sheets.'
    };

    // Foundation messages
    static FOUNDATIONS = {
        NO_FOUNDATIONS: '🏛️ Você ainda não tem fundamentos definidos.\n\nUse "adicionar fundamento [nome]" para criar seu primeiro fundamento.',
        LIST_HEADER: '🏛️ *Seus Fundamentos de Vida*\n\n',
        FOUNDATION_ADDED: '✅ Fundamento "{name}" adicionado com sucesso!',
        FOUNDATION_UPDATED: '✅ Fundamento "{name}" atualizado!',
        FOUNDATION_NOT_FOUND: '❌ Fundamento "{identifier}" não encontrado.',
        MAX_FOUNDATIONS: '❌ Máximo de 5 fundamentos permitidos por usuário.',
        DUPLICATE_FOUNDATION: '❌ Fundamento "{name}" já existe.',
        SCORE_UPDATED: '📊 Pontuação do fundamento "{name}" atualizada para {score}/{target}!'
    };

    // Quest messages
    static QUESTS = {
        NO_QUESTS: '🎯 Você ainda não tem missões ativas.\n\nUse "adicionar missão [título]" para criar sua primeira missão de 3 meses.',
        LIST_HEADER: '🎯 *Suas Missões (3 meses)*\n\n',
        QUEST_ADDED: '✅ Missão "{title}" criada com sucesso!\n📅 Prazo: {dueDate}',
        QUEST_UPDATED: '✅ Missão "{title}" atualizada!',
        QUEST_NOT_FOUND: '❌ Missão "{identifier}" não encontrada.',
        PROGRESS_UPDATED: '📊 Progresso da missão "{title}" atualizado para {progress}%!',
        QUEST_COMPLETED: '🎉 Parabéns! Missão "{title}" concluída!',
        DUPLICATE_QUEST: '❌ Missão ativa "{title}" já existe.'
    };

    // Portuguese keywords for AI processing
    static KEYWORDS = {
        ADD: ['adicionar', 'criar', 'nova', 'novo', 'lembrar', 'tarefa', 'fazer'],
        LIST: ['listar', 'mostrar', 'exibir', 'tarefas', 'lista', 'ver', 'quais'],
        COMPLETE: ['concluir', 'feito', 'finalizar', 'marcar', 'completar', 'terminar'],
        DELETE: ['excluir', 'remover', 'cancelar', 'apagar', 'deletar'],
        HELP: ['ajuda', 'como', 'o que posso', 'comandos', 'socorro'],
        STATS: ['estatísticas', 'resumo', 'visão geral', 'números', 'dados'],
        FOUNDATIONS: ['fundamento', 'fundamentos', 'base', 'bases', 'pilar', 'pilares'],
        QUESTS: ['missão', 'missões', 'quest', 'quests', 'objetivo', 'objetivos', 'meta', 'metas'],
        PRIORITY: {
            HIGH: ['urgente', 'importante', 'prioridade alta', 'crítico'],
            LOW: ['baixa prioridade', 'quando possível', 'não urgente']
        },
        FILTERS: {
            OVERDUE: ['atrasadas', 'vencidas', 'em atraso'],
            TODAY: ['hoje', 'para hoje'],
            COMPLETED: ['concluídas', 'feitas', 'finalizadas'],
            PENDING: ['pendentes', 'incompletas', 'em aberto']
        }
    };

    // Date expressions in Portuguese
    static DATE_EXPRESSIONS = {
        TODAY: ['hoje', 'hj'],
        TOMORROW: ['amanhã', 'amanha'],
        YESTERDAY: ['ontem'],
        DAYS: ['segunda', 'terça', 'quarta', 'quinta', 'sexta', 'sábado', 'domingo'],
        DAYS_SHORT: ['seg', 'ter', 'qua', 'qui', 'sex', 'sab', 'dom'],
        MONTHS: [
            'janeiro', 'fevereiro', 'março', 'abril', 'maio', 'junho',
            'julho', 'agosto', 'setembro', 'outubro', 'novembro', 'dezembro'
        ],
        RELATIVE: {
            'em': 'in',
            'daqui': 'in',
            'próximo': 'next',
            'próxima': 'next',
            'semana': 'week',
            'mês': 'month',
            'ano': 'year',
            'dias': 'days',
            'dia': 'day'
        }
    };

    /**
     * Get formatted help message
     */
    static getHelpMessage() {
        return this.HELP.TITLE + 
               this.HELP.INTRO +
               this.HELP.ADDING +
               this.HELP.ADDING_EXAMPLES.join('\n') +
               this.HELP.VIEWING +
               this.HELP.VIEWING_EXAMPLES.join('\n') +
               this.HELP.COMPLETING +
               this.HELP.COMPLETING_EXAMPLES.join('\n') +
               this.HELP.DELETING +
               this.HELP.DELETING_EXAMPLES.join('\n') +
               this.HELP.OTHER +
               this.HELP.OTHER_EXAMPLES.join('\n') +
               this.HELP.FOOTER;
    }

    /**
     * Format todo added message
     */
    static formatTodoAdded(title, dueDate = null) {
        let message = `${this.SUCCESS.TODO_ADDED} "${title}"`;
        if (dueDate) {
            message += ` (${this.DATES.DUE}: ${dueDate})`;
        }
        return message;
    }

    /**
     * Format todo completed message
     */
    static formatTodoCompleted(title) {
        return `${this.SUCCESS.TODO_COMPLETED} "${title}"!`;
    }

    /**
     * Format todo deleted message
     */
    static formatTodoDeleted(title) {
        return `${this.SUCCESS.TODO_DELETED} "${title}"`;
    }

    /**
     * Format todo updated message
     */
    static formatTodoUpdated(title) {
        return `${this.SUCCESS.TODO_UPDATED} "${title}"`;
    }

    /**
     * Format todo not found message
     */
    static formatTodoNotFound(identifier) {
        return `❌ ${this.ERROR.TODO_NOT_FOUND} ${identifier}`;
    }

    /**
     * Format unknown intent message
     */
    static formatUnknownIntent(messageText) {
        return `${this.ERROR.UNKNOWN_INTENT} "${messageText}"`;
    }

    /**
     * Format max todos reached error
     */
    static formatMaxTodosReached(maxTodos) {
        return `${this.ERROR.MAX_TODOS_REACHED} ${maxTodos} tarefas`;
    }

    /**
     * Format due date for display
     */
    static formatDueDate(dateString) {
        if (!dateString) return '';

        const date = new Date(dateString);
        const today = new Date();
        const tomorrow = new Date(today);
        tomorrow.setDate(today.getDate() + 1);

        if (date.toDateString() === today.toDateString()) {
            return this.DATES.TODAY;
        }
        if (date.toDateString() === tomorrow.toDateString()) {
            return this.DATES.TOMORROW;
        }

        return date.toLocaleDateString('pt-BR');
    }

    /**
     * Format stats message
     */
    static formatStats(stats) {
        let response = this.STATS.TITLE;
        response += `${this.STATS.TOTAL} ${stats.total}\n`;
        response += `${this.STATS.PENDING} ${stats.pending}\n`;
        response += `${this.STATS.COMPLETED} ${stats.completed}\n`;

        if (stats.overdue > 0) {
            response += `${this.STATS.OVERDUE} ${stats.overdue}\n`;
        }

        if (stats.dueToday > 0) {
            response += `${this.STATS.DUE_TODAY} ${stats.dueToday}\n`;
        }

        if (stats.total > 0) {
            const completionRate = Math.round((stats.completed / stats.total) * 100);
            response += `${this.STATS.COMPLETION_RATE} ${completionRate}%`;
        }

        return response;
    }

    /**
     * Format todo list summary
     */
    static formatListSummary(stats) {
        let summary = `${this.LISTS.SUMMARY} ${stats.pending} ${this.LISTS.PENDING}, ${stats.completed} ${this.LISTS.COMPLETED_COUNT}`;
        if (stats.overdue > 0) {
            summary += `, ${stats.overdue} ${this.LISTS.OVERDUE}`;
        }
        return summary;
    }

    static formatUnknownIntent(messageText) {
        return this.ERROR.UNDERSTANDING.replace('{message}', messageText);
    }

    // Foundation formatting methods
    static formatFoundationAdded(name) {
        return this.FOUNDATIONS.FOUNDATION_ADDED.replace('{name}', name);
    }

    static formatFoundationUpdated(name) {
        return this.FOUNDATIONS.FOUNDATION_UPDATED.replace('{name}', name);
    }

    static formatFoundationNotFound(identifier) {
        return this.FOUNDATIONS.FOUNDATION_NOT_FOUND.replace('{identifier}', identifier);
    }

    static formatFoundationScoreUpdated(name, score, target) {
        return this.FOUNDATIONS.SCORE_UPDATED
            .replace('{name}', name)
            .replace('{score}', score)
            .replace('{target}', target);
    }

    // Quest formatting methods
    static formatQuestAdded(title, dueDate) {
        return this.QUESTS.QUEST_ADDED
            .replace('{title}', title)
            .replace('{dueDate}', dueDate);
    }

    static formatQuestUpdated(title) {
        return this.QUESTS.QUEST_UPDATED.replace('{title}', title);
    }

    static formatQuestNotFound(identifier) {
        return this.QUESTS.QUEST_NOT_FOUND.replace('{identifier}', identifier);
    }

    static formatQuestProgressUpdated(title, progress) {
        return this.QUESTS.PROGRESS_UPDATED
            .replace('{title}', title)
            .replace('{progress}', progress);
    }

    static formatQuestCompleted(title) {
        return this.QUESTS.QUEST_COMPLETED.replace('{title}', title);
    }
}
