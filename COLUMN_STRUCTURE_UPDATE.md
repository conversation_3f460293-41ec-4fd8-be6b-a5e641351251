# Google Sheets Column Structure Update

## Overview
This document summarizes the comprehensive update to the Google Sheets column structures to match the user's specific business requirements with proper correlations between <PERSON><PERSON><PERSON> (todos), Objetivos (quests), and Fundações (foundations).

## Updated Column Structures

### 1. <PERSON><PERSON><PERSON> (Todos) - 15 Columns
Previously: 8 columns (ID, User Phone, Title, Due Date, Priority, Completed, Created At, Updated At)

**New Structure:**
1. **Missão** - Task title/name
2. **Property** - Category/property of the task
3. **Due before** - Due date
4. **Status** - Task status (Não iniciado, Em progresso, Concluído)
5. **Obs** - Observations/notes
6. **Fundação** - Related foundation (correlation)
7. **Quest 2025** - Related quest/objective (correlation)
8. **Books** - Related books/resources
9. **URL** - Reference URL
10. **Created time** - Creation timestamp
11. **Last edited time** - Last modification timestamp
12. **Estudo** - Study area/subject
13. **Priority** - Task priority (high/medium/low)
14. **User Phone** - User identifier
15. **ID** - Unique task identifier

### 2. Fundações (Foundations) - 14 Columns
Previously: 9 columns (ID, User Phone, Name, Description, Target Score, Current Score, Status, Created At, Updated At)

**New Structure:**
1. **Name** - Foundation name
2. **Created** - Creation date
3. **Date** - Reference date
4. **Related to Actions (Property 1)** - Related actions
5. **Related to mission after Google** - Related missions
6. **Related to PRDs (Related OKR)** - Related PRDs/OKRs
7. **Related to Problems, Ideas, Opportunities (1)** - Related problems/opportunities
8. **Related to Roadmap (OKR)** - Related roadmap items
9. **Status** - Foundation status (active/inactive/completed)
10. **Text** - Detailed description
11. **Total progress 25** - Progress score (0-25)
12. **🔑 Key Results** - Key results/outcomes
13. **User Phone** - User identifier
14. **ID** - Unique foundation identifier

### 3. Objetivos (Quests) - 18 Columns
Previously: 13 columns (ID, User Phone, Title, Description, Start Date, Due Date, Status, Progress %, Weekly Goal, Current Week, Measurement Metric, Created At, Updated At)

**New Structure:**
1. **Plano** - Plan/title of the objective
2. **Fundação** - Related foundation (correlation)
3. **Como?** - How it will be executed
4. **90d Target** - 90-day target
5. **90d YTD** - Year-to-date progress
6. **URL** - Reference URL
7. **Due Date** - Due date
8. **Created** - Creation timestamp
9. **Status** - Objective status (active/inactive/completed)
10. **Missão** - Related mission/task (correlation)
11. **Projects** - Related projects
12. **Como foi?** - How was the execution
13. **Initial Value** - Starting value/baseline
14. **Progress** - Progress percentage (0-100)
15. **Revisão e avaliação da quest** - Review and evaluation
16. **Feito** - Completion status (TRUE/FALSE)
17. **User Phone** - User identifier
18. **ID** - Unique objective identifier

## Key Correlations Implemented

### 1. Missões ↔ Fundações
- **Missões.Fundação** field links to **Fundações.Name**
- Allows tracking which life foundation each task supports

### 2. Missões ↔ Objetivos
- **Missões.Quest 2025** field links to **Objetivos.Plano**
- Enables connecting daily tasks to 3-month objectives

### 3. Objetivos ↔ Fundações
- **Objetivos.Fundação** field links to **Fundações.Name**
- Ensures objectives are aligned with life foundations

### 4. Bidirectional References
- **Objetivos.Missão** can reference **Missões.Missão**
- **Fundações** have multiple relationship fields for comprehensive tracking

## Files Updated

### Core Service Files
- **src/sheets/sheetsService.js**
  - Updated `ensureTodosHeaders()` method
  - Updated `ensureFoundationsHeaders()` method  
  - Updated `ensureQuestsHeaders()` method
  - Updated `addTodo()` method with new 15-column structure
  - Updated `addFoundation()` method with new 14-column structure
  - Updated `addQuest()` method with new 18-column structure
  - Updated return objects to include all new fields

### Manager Files
- **src/todo/todoManager.js**
  - Updated `cleanTodoData` object to include all new fields
  - Added support for foundation and quest correlations

### AI Processing
- **src/ai/aiProcessor.js**
  - Updated system prompt to include new field structures
  - Added correlation awareness for AI processing
  - Updated examples to show foundation relationships

## Testing
- Created **test-sheets-structure.js** for comprehensive testing
- Updated **test-integrations.js** with correlation examples
- All tests verify new column structures and correlations

## Benefits of New Structure

1. **Enhanced Correlation**: Clear relationships between todos, foundations, and quests
2. **Better Tracking**: More detailed fields for comprehensive project management
3. **Improved Organization**: Separate fields for different types of information
4. **Scalability**: Structure supports complex project hierarchies
5. **Business Alignment**: Matches user's specific workflow requirements

## Migration Notes
- Old data structure is preserved in field mapping
- New fields default to empty strings where not provided
- Backward compatibility maintained for existing functionality
- Range references updated (A:H → A:O for Missões, etc.)

## Next Steps
1. Test with actual Google Sheets integration
2. Verify all CRUD operations work correctly
3. Update any remaining methods that reference old column indices
4. Add validation for correlation fields
5. Implement correlation lookup functionality in UI
