import { DateParser } from '../src/utils/dateParser.js';

describe('DateParser', () => {
    let dateParser;

    beforeEach(() => {
        dateParser = new DateParser();
    });

    describe('parseDate', () => {
        test('should return null for empty input', () => {
            expect(dateParser.parseDate('')).toBeNull();
            expect(dateParser.parseDate(null)).toBeNull();
        });

        test('should return YYYY-MM-DD format unchanged', () => {
            expect(dateParser.parseDate('2024-01-15')).toBe('2024-01-15');
        });

        test('should parse "today" correctly', () => {
            const today = new Date().toISOString().split('T')[0];
            expect(dateParser.parseDate('today')).toBe(today);
        });

        test('should parse "tomorrow" correctly', () => {
            const tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            const tomorrowStr = tomorrow.toISOString().split('T')[0];
            expect(dateParser.parseDate('tomorrow')).toBe(tomorrowStr);
        });
    });

    describe('extractDateFromText', () => {
        test('should extract "today" from text', () => {
            const result = dateParser.extractDateFromText('buy groceries today');
            expect(result.dateText).toBe('today');
            expect(result.date).toBe(new Date().toISOString().split('T')[0]);
        });

        test('should extract "tomorrow" from text', () => {
            const result = dateParser.extractDateFromText('call mom tomorrow');
            expect(result.dateText).toBe('tomorrow');
            const tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            expect(result.date).toBe(tomorrow.toISOString().split('T')[0]);
        });

        test('should return null for text without dates', () => {
            const result = dateParser.extractDateFromText('buy groceries');
            expect(result.date).toBeNull();
            expect(result.dateText).toBeNull();
        });
    });

    describe('isOverdue', () => {
        test('should return true for past dates', () => {
            const yesterday = new Date();
            yesterday.setDate(yesterday.getDate() - 1);
            const yesterdayStr = yesterday.toISOString().split('T')[0];
            expect(dateParser.isOverdue(yesterdayStr)).toBe(true);
        });

        test('should return false for today', () => {
            const today = new Date().toISOString().split('T')[0];
            expect(dateParser.isOverdue(today)).toBe(false);
        });

        test('should return false for future dates', () => {
            const tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            const tomorrowStr = tomorrow.toISOString().split('T')[0];
            expect(dateParser.isOverdue(tomorrowStr)).toBe(false);
        });
    });

    describe('isToday', () => {
        test('should return true for today', () => {
            const today = new Date().toISOString().split('T')[0];
            expect(dateParser.isToday(today)).toBe(true);
        });

        test('should return false for other dates', () => {
            const tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            const tomorrowStr = tomorrow.toISOString().split('T')[0];
            expect(dateParser.isToday(tomorrowStr)).toBe(false);
        });
    });

    describe('formatDate', () => {
        test('should format today as "Today"', () => {
            const today = new Date().toISOString().split('T')[0];
            expect(dateParser.formatDate(today)).toBe('Today');
        });

        test('should format tomorrow as "Tomorrow"', () => {
            const tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            const tomorrowStr = tomorrow.toISOString().split('T')[0];
            expect(dateParser.formatDate(tomorrowStr)).toBe('Tomorrow');
        });

        test('should format other dates properly', () => {
            const result = dateParser.formatDate('2024-12-25');
            expect(result).toMatch(/Dec 25, 2024/);
        });
    });
});
