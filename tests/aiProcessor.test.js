import { AIProcessor } from '../src/ai/aiProcessor.js';

// Mock OpenAI
jest.mock('openai', () => {
    return {
        __esModule: true,
        default: jest.fn().mockImplementation(() => ({
            chat: {
                completions: {
                    create: jest.fn()
                }
            }
        }))
    };
});

describe('AIProcessor', () => {
    let aiProcessor;

    beforeEach(() => {
        aiProcessor = new AIProcessor();
    });

    describe('fallbackProcessing', () => {
        test('should identify add todo messages', () => {
            const result = aiProcessor.fallbackProcessing('add buy groceries');
            expect(result.action).toBe('add_todo');
            expect(result.todoData.title).toBe('buy groceries');
        });

        test('should identify list todo messages', () => {
            const result = aiProcessor.fallbackProcessing('list my todos');
            expect(result.action).toBe('list_todos');
        });

        test('should identify complete todo messages', () => {
            const result = aiProcessor.fallbackProcessing('mark groceries as done');
            expect(result.action).toBe('complete_todo');
            expect(result.todoIdentifier).toContain('groceries');
        });

        test('should identify delete todo messages', () => {
            const result = aiProcessor.fallbackProcessing('delete groceries task');
            expect(result.action).toBe('delete_todo');
            expect(result.todoIdentifier).toContain('groceries');
        });

        test('should identify help messages', () => {
            const result = aiProcessor.fallbackProcessing('help');
            expect(result.action).toBe('help');
        });

        test('should identify stats messages', () => {
            const result = aiProcessor.fallbackProcessing('stats');
            expect(result.action).toBe('stats');
        });

        test('should return unknown for unrecognized messages', () => {
            const result = aiProcessor.fallbackProcessing('random text');
            expect(result.action).toBe('unknown');
        });
    });

    describe('extractTodoFromMessage', () => {
        test('should extract todo title correctly', () => {
            const result = aiProcessor.extractTodoFromMessage('add buy groceries tomorrow');
            expect(result.todoData.title).toBe('buy groceries');
        });

        test('should extract priority from urgent keywords', () => {
            const result = aiProcessor.extractTodoFromMessage('add urgent task');
            expect(result.todoData.priority).toBe('high');
        });

        test('should default to medium priority', () => {
            const result = aiProcessor.extractTodoFromMessage('add normal task');
            expect(result.todoData.priority).toBe('medium');
        });
    });

    describe('extractTodoIdentifier', () => {
        test('should extract quoted text', () => {
            const identifier = aiProcessor.extractTodoIdentifier('mark "buy groceries" as done');
            expect(identifier).toBe('buy groceries');
        });

        test('should extract text after action words', () => {
            const identifier = aiProcessor.extractTodoIdentifier('complete groceries task');
            expect(identifier).toBe('groceries task');
        });
    });

    describe('extractPriority', () => {
        test('should identify high priority keywords', () => {
            expect(aiProcessor.extractPriority('urgent task')).toBe('high');
            expect(aiProcessor.extractPriority('important meeting')).toBe('high');
            expect(aiProcessor.extractPriority('asap call')).toBe('high');
        });

        test('should identify low priority keywords', () => {
            expect(aiProcessor.extractPriority('low priority task')).toBe('low');
            expect(aiProcessor.extractPriority('when possible')).toBe('low');
        });

        test('should default to medium priority', () => {
            expect(aiProcessor.extractPriority('normal task')).toBe('medium');
        });
    });
});
