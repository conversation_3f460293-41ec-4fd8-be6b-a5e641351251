import dotenv from 'dotenv';
import { GoogleGenerativeAI } from '@google/generative-ai';
import { CalendarService } from './src/calendar/calendarService.js';
import { AIProcessor } from './src/ai/aiProcessor.js';

// Load environment variables
dotenv.config();

async function testGeminiIntegration() {
    console.log('🧪 Testing Gemini AI Integration...');
    
    try {
        if (!process.env.GEMINI_API_KEY) {
            console.log('❌ GEMINI_API_KEY not found in environment variables');
            return false;
        }

        const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
        const model = genAI.getGenerativeModel({ model: "gemini-1.5-flash" });

        const prompt = "Responda apenas com 'OK' se você conseguir me entender em português.";
        const result = await model.generateContent(prompt);
        const response = await result.response;
        const text = response.text();

        console.log('✅ Gemini AI Response:', text.trim());
        return true;
    } catch (error) {
        console.log('❌ Gemini AI Test Failed:', error.message);
        return false;
    }
}

async function testCalendarService() {
    console.log('🧪 Testing Google Calendar Service...');
    
    try {
        const calendarService = new CalendarService();
        await calendarService.initialize();
        
        console.log('✅ Google Calendar Service initialized successfully');
        
        // Test creating a sample event
        const eventData = {
            title: 'Teste WhatsApp Bot',
            description: 'Evento de teste criado pelo bot',
            startDate: new Date(Date.now() + 60 * 60 * 1000), // 1 hour from now
            type: 'todo',
            userPhone: 'test-user'
        };
        
        const result = await calendarService.createEvent(eventData);
        if (result.success) {
            console.log('✅ Test event created:', result.eventId);
            
            // Clean up - delete the test event
            await calendarService.deleteEvent(result.eventId);
            console.log('✅ Test event cleaned up');
        } else {
            console.log('❌ Failed to create test event:', result.error);
        }
        
        return true;
    } catch (error) {
        console.log('❌ Calendar Service Test Failed:', error.message);
        return false;
    }
}

async function testAIProcessor() {
    console.log('🧪 Testing AI Processor with Gemini...');
    
    try {
        const aiProcessor = new AIProcessor();
        
        const testMessage = "Lembrar de comprar leite hoje";
        const result = await aiProcessor.processMessage(testMessage);
        
        console.log('✅ AI Processor Result:', JSON.stringify(result, null, 2));
        
        if (result.action === 'add_todo' && result.todoData && result.todoData.title) {
            console.log('✅ AI correctly identified todo creation intent');
            return true;
        } else {
            console.log('❌ AI did not correctly identify the intent');
            return false;
        }
    } catch (error) {
        console.log('❌ AI Processor Test Failed:', error.message);
        return false;
    }
}

async function runAllTests() {
    console.log('🚀 Starting Integration Tests...\n');
    
    const results = {
        gemini: await testGeminiIntegration(),
        calendar: await testCalendarService(),
        aiProcessor: await testAIProcessor()
    };
    
    console.log('\n📊 Test Results Summary:');
    console.log('========================');
    console.log(`Gemini AI: ${results.gemini ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`Google Calendar: ${results.calendar ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`AI Processor: ${results.aiProcessor ? '✅ PASS' : '❌ FAIL'}`);
    
    const allPassed = Object.values(results).every(result => result === true);
    console.log(`\n🎯 Overall: ${allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);
    
    if (allPassed) {
        console.log('\n🎉 Your WhatsApp Todo Bot is ready with:');
        console.log('   • Gemini AI for natural language processing');
        console.log('   • Google Calendar sync for todos and quests');
        console.log('   • Portuguese language support');
        console.log('   • Foundation and Quest management');
    } else {
        console.log('\n⚠️  Please check your configuration:');
        console.log('   • Ensure GEMINI_API_KEY is set in .env');
        console.log('   • Ensure Google service account has Calendar API access');
        console.log('   • Ensure GOOGLE_CALENDAR_ID is set in .env');
    }
}

// Run tests
runAllTests().catch(console.error);
