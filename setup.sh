#!/bin/bash

# WhatsApp Todo Bot Setup Script

echo "🤖 Setting up WhatsApp Todo Bot..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ first."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js version 18+ is required. Current version: $(node -v)"
    exit 1
fi

echo "✅ Node.js $(node -v) detected"

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo "📝 Creating .env file..."
    cp .env.example .env
    echo "✅ Created .env file from template"
    echo "⚠️  Please edit .env file with your API keys and configuration"
else
    echo "✅ .env file already exists"
fi

# Create config directory
mkdir -p config
mkdir -p logs

# Check if service account key exists
if [ ! -f config/service-account-key.json ]; then
    echo "⚠️  Google Service Account key not found at config/service-account-key.json"
    echo "   Please follow the instructions in config/README.md to set it up"
fi

echo ""
echo "🎉 Setup complete!"
echo ""
echo "Next steps:"
echo "1. Edit .env file with your API keys"
echo "2. Set up Google Service Account (see config/README.md)"
echo "3. Create a Google Sheet and share it with your service account"
echo "4. Run 'npm start' to start the bot"
echo ""
echo "For help, run 'npm run help' or check the README.md file"
