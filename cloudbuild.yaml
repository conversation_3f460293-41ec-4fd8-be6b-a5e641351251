# Google Cloud Build configuration
steps:
  # Build the container image
  - name: 'gcr.io/cloud-builders/docker'
    args: ['build', '-t', 'gcr.io/$PROJECT_ID/whatsapp-todo-bot:$COMMIT_SHA', '.']
  
  # Push the container image to Container Registry
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/whatsapp-todo-bot:$COMMIT_SHA']
  
  # Deploy container image to Cloud Run
  - name: 'gcr.io/cloud-builders/gcloud'
    args:
    - 'run'
    - 'deploy'
    - 'whatsapp-todo-bot'
    - '--image'
    - 'gcr.io/$PROJECT_ID/whatsapp-todo-bot:$COMMIT_SHA'
    - '--region'
    - 'us-central1'
    - '--platform'
    - 'managed'
    - '--allow-unauthenticated'
    - '--memory'
    - '1Gi'
    - '--cpu'
    - '1'
    - '--max-instances'
    - '1'
    - '--set-env-vars'
    - 'NODE_ENV=production'

images:
  - 'gcr.io/$PROJECT_ID/whatsapp-todo-bot:$COMMIT_SHA'
