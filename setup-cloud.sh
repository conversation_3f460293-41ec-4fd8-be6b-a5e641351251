#!/bin/bash

echo "🚀 Setup para Hospedagem Gratuita 24/7"
echo "======================================"

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para imprimir com cores
print_step() {
    echo -e "${BLUE}📋 $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Verificar se está no diretório correto
if [ ! -f "package.json" ]; then
    print_error "Execute este script no diretório raiz do projeto!"
    exit 1
fi

print_step "Verificando dependências..."

# Verificar Node.js
if ! command -v node &> /dev/null; then
    print_error "Node.js não encontrado! Instale Node.js primeiro."
    exit 1
fi

# Verificar npm
if ! command -v npm &> /dev/null; then
    print_error "npm não encontrado! Instale npm primeiro."
    exit 1
fi

print_success "Node.js e npm encontrados"

# Instalar dependências se necessário
if [ ! -d "node_modules" ]; then
    print_step "Instalando dependências..."
    npm install
    print_success "Dependências instaladas"
fi

# Verificar se .env existe
if [ ! -f ".env" ]; then
    print_step "Criando arquivo .env..."
    cat > .env << EOF
# IA - Gemini (GRATUITO)
GEMINI_API_KEY=sua_chave_gemini_aqui

# Google Sheets (GRATUITO)
GOOGLE_SPREADSHEET_ID=seu_id_da_planilha
GOOGLE_SERVICE_ACCOUNT_KEY_PATH=./credentials/service-account-key.json

# Google Calendar (GRATUITO)
GOOGLE_CALENDAR_ID=<EMAIL>

# Configurações do Bot
MAX_TODOS_PER_USER=100
MAX_FOUNDATIONS_PER_USER=5

# Porta para hospedagem em nuvem
PORT=3000
EOF
    print_success "Arquivo .env criado"
    print_warning "IMPORTANTE: Configure as variáveis no arquivo .env"
else
    print_success "Arquivo .env já existe"
fi

# Criar diretório de credenciais
if [ ! -d "credentials" ]; then
    print_step "Criando diretório de credenciais..."
    mkdir -p credentials
    print_success "Diretório credentials criado"
    print_warning "Coloque o arquivo service-account-key.json em ./credentials/"
fi

# Verificar se os arquivos de deploy existem
print_step "Verificando arquivos de deploy..."

files_needed=("railway.json" "Dockerfile" "DEPLOY_GUIDE.md")
all_files_exist=true

for file in "${files_needed[@]}"; do
    if [ ! -f "$file" ]; then
        print_warning "Arquivo $file não encontrado"
        all_files_exist=false
    fi
done

if [ "$all_files_exist" = true ]; then
    print_success "Todos os arquivos de deploy estão prontos"
else
    print_warning "Alguns arquivos de deploy estão faltando"
fi

# Testar se o bot pode inicializar
print_step "Testando inicialização do bot..."

# Criar um teste rápido
cat > test-startup.js << 'EOF'
import dotenv from 'dotenv';
dotenv.config();

console.log('🧪 Testando configuração...');

// Verificar variáveis essenciais
const requiredVars = ['GEMINI_API_KEY', 'GOOGLE_SPREADSHEET_ID'];
let missingVars = [];

requiredVars.forEach(varName => {
    if (!process.env[varName] || process.env[varName].includes('sua_')) {
        missingVars.push(varName);
    }
});

if (missingVars.length > 0) {
    console.log('❌ Variáveis não configuradas:', missingVars.join(', '));
    console.log('⚠️  Configure essas variáveis no arquivo .env');
    process.exit(1);
} else {
    console.log('✅ Configuração básica OK');
}

// Testar importações
try {
    const { TodoBotApp } = await import('./src/index.js');
    console.log('✅ Importações funcionando');
} catch (error) {
    console.log('❌ Erro nas importações:', error.message);
    process.exit(1);
}

console.log('🎉 Bot pronto para deploy!');
EOF

# Executar teste
if node test-startup.js 2>/dev/null; then
    print_success "Bot configurado corretamente"
else
    print_warning "Bot precisa de configuração adicional"
    print_warning "Configure as variáveis no arquivo .env"
fi

# Limpar arquivo de teste
rm -f test-startup.js

echo ""
echo "🎯 PRÓXIMOS PASSOS PARA HOSPEDAGEM GRATUITA 24/7:"
echo "================================================="
echo ""
echo "1. 🔑 Configure as chaves no arquivo .env:"
echo "   - GEMINI_API_KEY (gratuito em https://aistudio.google.com)"
echo "   - GOOGLE_SPREADSHEET_ID (ID da sua planilha)"
echo "   - GOOGLE_CALENDAR_ID (seu email)"
echo ""
echo "2. 📁 Adicione o arquivo de credenciais:"
echo "   - Baixe service-account-key.json do Google Cloud"
echo "   - Coloque em ./credentials/service-account-key.json"
echo ""
echo "3. ☁️  Escolha uma opção de hospedagem gratuita:"
echo "   - Railway.app (RECOMENDADO - 500h gratuitas/mês)"
echo "   - Render.com (tier gratuito)"
echo "   - Google Cloud Run (2M requisições gratuitas/mês)"
echo ""
echo "4. 🚀 Deploy:"
echo "   - Suba o código para GitHub"
echo "   - Conecte no Railway/Render"
echo "   - Configure as variáveis de ambiente"
echo "   - Deploy automático!"
echo ""
echo "📖 Guia completo: Leia o arquivo DEPLOY_GUIDE.md"
echo ""
print_success "Setup concluído! Seu bot está pronto para hospedagem 24/7 gratuita!"
