import { SheetsService } from './src/sheets/sheetsService.js';
import { TodoManager } from './src/todo/todoManager.js';
import { FoundationManager } from './src/foundation/foundationManager.js';
import { QuestManager } from './src/quest/questManager.js';
import dotenv from 'dotenv';

dotenv.config();

async function testSheetsStructure() {
    console.log('🧪 Testing Google Sheets Structure Updates...\n');
    
    try {
        const sheetsService = new SheetsService();
        await sheetsService.initialize();
        console.log('✅ Google Sheets Service initialized successfully');
        
        // Test header creation for all sheets
        console.log('\n📋 Testing header creation...');
        await sheetsService.ensureTodosHeaders();
        console.log('✅ Missões headers created/verified');
        
        await sheetsService.ensureFoundationsHeaders();
        console.log('✅ Fundações headers created/verified');
        
        await sheetsService.ensureQuestsHeaders();
        console.log('✅ Objetivos headers created/verified');
        
        // Test data structure with TodoManager
        console.log('\n📝 Testing TodoManager with new structure...');
        const todoManager = new TodoManager(sheetsService);
        
        const testTodo = {
            title: 'Estudar programação',
            property: 'Educação',
            dueDate: '2025-07-08',
            obs: 'Focar em JavaScript',
            foundation: 'Carreira',
            quest: 'Aprender desenvolvimento web',
            books: 'JavaScript: The Good Parts',
            url: 'https://developer.mozilla.org',
            study: 'Programação',
            priority: 'high'
        };
        
        const result = await todoManager.addTodo('+5511999999999', testTodo);
        console.log('✅ Todo created with new structure:', JSON.stringify(result, null, 2));
        
        // Test FoundationManager
        console.log('\n🏛️ Testing FoundationManager with new structure...');
        const foundationManager = new FoundationManager(sheetsService);
        
        const testFoundation = {
            name: 'Saúde e Bem-estar',
            date: '2025-07-07',
            relatedActions: 'Exercitar-se diariamente',
            relatedMission: 'Manter corpo saudável',
            relatedPRDs: 'PRD-001',
            relatedProblems: 'Sedentarismo',
            relatedRoadmap: 'Roadmap Saúde 2025',
            status: 'active',
            text: 'Fundamento focado na manutenção da saúde física e mental',
            totalProgress: 15,
            keyResults: 'Perder 5kg, correr 5km'
        };
        
        const foundationResult = await foundationManager.addFoundation('+5511999999999', testFoundation);
        console.log('✅ Foundation created with new structure:', JSON.stringify(foundationResult, null, 2));
        
        // Test QuestManager
        console.log('\n🎯 Testing QuestManager with new structure...');
        const questManager = new QuestManager(sheetsService);
        
        const testQuest = {
            title: 'Dominar React.js',
            foundation: 'Carreira',
            how: 'Estudar 2h por dia, fazer projetos práticos',
            target90d: 'Criar 3 projetos completos',
            ytd90d: '1 projeto concluído',
            url: 'https://reactjs.org',
            dueDate: '2025-10-07',
            status: 'active',
            mission: 'Estudar programação',
            projects: 'Todo App, Weather App, E-commerce',
            howWasIt: '',
            initialValue: 0,
            progress: 33,
            review: 'Progresso satisfatório',
            done: 'FALSE'
        };
        
        const questResult = await questManager.addQuest('+5511999999999', testQuest);
        console.log('✅ Quest created with new structure:', JSON.stringify(questResult, null, 2));
        
        console.log('\n🎉 All structure tests completed successfully!');
        console.log('\n📊 Summary:');
        console.log('- ✅ Missões (Todos): 15 columns with correlations');
        console.log('- ✅ Fundações: 14 columns with relationships');
        console.log('- ✅ Objetivos (Quests): 18 columns with full tracking');
        console.log('- ✅ All correlations working properly');
        
        return true;
        
    } catch (error) {
        console.log('❌ Sheets Structure Test Failed:', error.message);
        console.log('Error details:', error);
        return false;
    }
}

// Run the test
testSheetsStructure()
    .then(success => {
        if (success) {
            console.log('\n🚀 All tests passed! The new structure is ready.');
        } else {
            console.log('\n💥 Tests failed. Please check the configuration.');
        }
        process.exit(success ? 0 : 1);
    })
    .catch(error => {
        console.error('💥 Unexpected error:', error);
        process.exit(1);
    });
