# Dockerfile otimizado para Google Cloud Run
FROM node:20-alpine

# Instalar git (necessário para <PERSON>)
RUN apk add --no-cache git

# Configurar ambiente Node.js
ENV NODE_ENV=production

# Criar usuário não-root para segurança
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001

# Criar diretório da aplicação
WORKDIR /app

# Copiar package.json e package-lock.json
COPY package*.json ./

# Instalar dependências
RUN npm ci --only=production && npm cache clean --force

# Copiar código da aplicação
COPY . .

# Criar diretório para credenciais e sessão Baileys
RUN mkdir -p credentials baileys_auth && \
    chown -R nodejs:nodejs /app

# Mudar para usuário não-root
USER nodejs

# Expor porta (Cloud Run usa PORT env var)
EXPOSE 8080

# Comando para iniciar a aplicação
CMD ["npm", "start"]
