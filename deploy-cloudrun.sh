#!/bin/bash

# Script para deploy no Google Cloud Run
# Execute: ./deploy-cloudrun.sh

set -e

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_step() {
    echo -e "${BLUE}📋 $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

echo "🚀 Deploy WhatsApp Bot no Google Cloud Run"
echo "=========================================="

# Verificar se gcloud está instalado
if ! command -v gcloud &> /dev/null; then
    print_error "Google Cloud CLI não encontrado!"
    echo "Instale em: https://cloud.google.com/sdk/docs/install"
    exit 1
fi

# Verificar se está logado
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
    print_warning "Não está logado no Google Cloud"
    print_step "Fazendo login..."
    gcloud auth login
fi

# Verificar/configurar projeto
PROJECT_ID=$(gcloud config get-value project 2>/dev/null)
if [ -z "$PROJECT_ID" ]; then
    print_warning "Nenhum projeto configurado"
    echo "Projetos disponíveis:"
    gcloud projects list
    echo ""
    read -p "Digite o ID do projeto: " PROJECT_ID
    gcloud config set project $PROJECT_ID
fi

print_success "Usando projeto: $PROJECT_ID"

# Habilitar APIs necessárias
print_step "Habilitando APIs necessárias..."
gcloud services enable cloudbuild.googleapis.com
gcloud services enable run.googleapis.com
gcloud services enable containerregistry.googleapis.com
print_success "APIs habilitadas"

# Configurar região
REGION="us-central1"
gcloud config set run/region $REGION
print_success "Região configurada: $REGION"

# Verificar se .env existe
if [ ! -f ".env" ]; then
    print_error "Arquivo .env não encontrado!"
    print_warning "Crie o arquivo .env com suas configurações"
    exit 1
fi

# Ler variáveis do .env
print_step "Lendo configurações do .env..."
export $(grep -v '^#' .env | xargs)

# Verificar variáveis essenciais
REQUIRED_VARS=("GEMINI_API_KEY" "GOOGLE_SPREADSHEET_ID")
for var in "${REQUIRED_VARS[@]}"; do
    if [ -z "${!var}" ]; then
        print_error "Variável $var não configurada no .env"
        exit 1
    fi
done

# Verificar se arquivo de credenciais existe
if [ ! -f "./credentials/service-account-key.json" ]; then
    print_error "Arquivo de credenciais não encontrado!"
    print_warning "Coloque service-account-key.json em ./credentials/"
    exit 1
fi

# Build e deploy
print_step "Fazendo build da imagem..."
gcloud builds submit --tag gcr.io/$PROJECT_ID/whatsapp-todo-bot

print_step "Fazendo deploy no Cloud Run..."
gcloud run deploy whatsapp-todo-bot \
    --image gcr.io/$PROJECT_ID/whatsapp-todo-bot \
    --platform managed \
    --region $REGION \
    --allow-unauthenticated \
    --memory 1Gi \
    --cpu 1 \
    --max-instances 1 \
    --set-env-vars "GEMINI_API_KEY=$GEMINI_API_KEY,GOOGLE_SPREADSHEET_ID=$GOOGLE_SPREADSHEET_ID,GOOGLE_CALENDAR_ID=$GOOGLE_CALENDAR_ID,MAX_TODOS_PER_USER=${MAX_TODOS_PER_USER:-100},MAX_FOUNDATIONS_PER_USER=${MAX_FOUNDATIONS_PER_USER:-5},NODE_ENV=production"

# Obter URL do serviço
SERVICE_URL=$(gcloud run services describe whatsapp-todo-bot --region=$REGION --format="value(status.url)")

print_success "Deploy concluído!"
echo ""
echo "🎉 Seu bot está rodando em:"
echo "🔗 $SERVICE_URL"
echo ""
echo "📋 Próximos passos:"
echo "1. Acesse os logs: gcloud logs tail --follow --resource=cloud_run_revision --filter='resource.labels.service_name=whatsapp-todo-bot'"
echo "2. Escaneie o QR code do WhatsApp nos logs"
echo "3. Teste enviando uma mensagem para o bot"
echo ""
echo "💰 Custo: R$ 0,00 (dentro do tier gratuito)"
echo "📊 Monitoramento: https://console.cloud.google.com/run"

# Mostrar logs em tempo real
read -p "Deseja ver os logs em tempo real? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    print_step "Mostrando logs em tempo real..."
    gcloud logs tail --follow --resource=cloud_run_revision --filter="resource.labels.service_name=whatsapp-todo-bot"
fi
