# 🌟 Google Cloud Run - Deploy Gratuito 24/7

## Por que Google Cloud Run?

### ✅ **Vantagens:**
- **2 milhões de requisições gratuitas/mês** (muito generoso)
- **Sempre online** - não "dorme" como Render
- **Escala automaticamente** - mais eficiente que Railway
- **Infraestrutura do Google** - máxima confiabilidade
- **Logs avançados** - melhor debugging
- **Integração nativa** com outros serviços Google

### 📊 **Comparação de Limites Gratuitos:**

| Serviço | Requisições/mês | Uptime | Recursos |
|---------|-----------------|--------|----------|
| **Cloud Run** | 2 milhões | ✅ 24/7 | 1GB RAM, 1 CPU |
| Railway | ~500h/mês | ✅ 24/7 | 512MB RAM |
| Render | Ilimitado* | ⚠️ Dorme 15min | 512MB RAM |

*Mas dorme após inatividade

## 🚀 Setup Passo a Passo

### **1. Pré-requisitos (5 minutos)**

#### Instalar Google Cloud CLI:
```bash
# macOS
brew install google-cloud-sdk

# Windows
# Baixe de: https://cloud.google.com/sdk/docs/install

# Linux
curl https://sdk.cloud.google.com | bash
```

#### Criar/Configurar Projeto Google Cloud:
1. Vá para [Google Cloud Console](https://console.cloud.google.com)
2. Crie um novo projeto (gratuito)
3. Anote o PROJECT_ID

### **2. Configuração Local (5 minutos)**

```bash
# 1. Login no Google Cloud
gcloud auth login

# 2. Configurar projeto
gcloud config set project SEU_PROJECT_ID

# 3. Verificar configuração
./setup-cloud.sh
```

### **3. Deploy Automático (5 minutos)**

```bash
# Execute o script de deploy
./deploy-cloudrun.sh
```

O script faz tudo automaticamente:
- ✅ Habilita APIs necessárias
- ✅ Faz build da imagem Docker
- ✅ Deploy no Cloud Run
- ✅ Configura variáveis de ambiente
- ✅ Mostra logs em tempo real

## 📱 **Como Funciona Depois do Deploy**

### **Primeira Conexão:**
1. Script mostra logs em tempo real
2. Você vê o QR code do WhatsApp nos logs
3. Escaneia com seu celular
4. Bot conecta e fica online 24/7

### **Uso Diário:**
- **WhatsApp funciona normalmente** de qualquer lugar
- **Google Sheets sempre atualizado**
- **Google Calendar sincronizado**
- **Logs disponíveis** para debugging

## 🔧 **Comandos Úteis**

### **Ver Logs:**
```bash
# Logs em tempo real
gcloud logs tail --follow --resource=cloud_run_revision --filter='resource.labels.service_name=whatsapp-todo-bot'

# Logs das últimas 24h
gcloud logs read --limit=50 --resource=cloud_run_revision --filter='resource.labels.service_name=whatsapp-todo-bot'
```

### **Atualizar Bot:**
```bash
# Após fazer mudanças no código
./deploy-cloudrun.sh
```

### **Ver Status:**
```bash
# Status do serviço
gcloud run services describe whatsapp-todo-bot --region=us-central1

# URL do serviço
gcloud run services describe whatsapp-todo-bot --region=us-central1 --format="value(status.url)"
```

### **Monitoramento:**
```bash
# Métricas de uso
gcloud run services describe whatsapp-todo-bot --region=us-central1 --format="table(status.traffic[].percent,status.traffic[].latestRevision)"
```

## 💰 **Monitoramento de Custos**

### **Ver Uso Atual:**
1. Vá para [Google Cloud Console](https://console.cloud.google.com)
2. Menu → Billing → Reports
3. Filtre por "Cloud Run"

### **Alertas de Billing:**
```bash
# Criar alerta se passar de R$ 5/mês
gcloud alpha billing budgets create \
    --billing-account=SEU_BILLING_ACCOUNT \
    --display-name="WhatsApp Bot Budget" \
    --budget-amount=5BRL
```

## 🔒 **Segurança e Boas Práticas**

### **Variáveis de Ambiente Seguras:**
```bash
# Usar Secret Manager para dados sensíveis
gcloud secrets create gemini-api-key --data-file=-
echo "sua_chave_aqui" | gcloud secrets create gemini-api-key --data-file=-

# Referenciar no Cloud Run
gcloud run deploy whatsapp-todo-bot \
    --set-secrets="GEMINI_API_KEY=gemini-api-key:latest"
```

### **Limitar Acesso:**
```bash
# Remover acesso público (se necessário)
gcloud run services remove-iam-policy-binding whatsapp-todo-bot \
    --member="allUsers" \
    --role="roles/run.invoker" \
    --region=us-central1
```

## 🚨 **Troubleshooting**

### **Bot Não Conecta:**
```bash
# Ver logs detalhados
gcloud logs read --limit=100 --resource=cloud_run_revision --filter='resource.labels.service_name=whatsapp-todo-bot AND severity>=ERROR'
```

### **Erro de Memória:**
```bash
# Aumentar memória (ainda gratuito)
gcloud run deploy whatsapp-todo-bot \
    --memory 2Gi \
    --region=us-central1
```

### **Timeout:**
```bash
# Aumentar timeout
gcloud run deploy whatsapp-todo-bot \
    --timeout=3600 \
    --region=us-central1
```

## 📊 **Vantagens vs Outras Opções**

### **Cloud Run vs Railway:**
- ✅ **Mais requisições gratuitas** (2M vs ~500h)
- ✅ **Melhor infraestrutura** (Google vs Railway)
- ✅ **Logs mais avançados**
- ⚠️ **Setup um pouco mais técnico**

### **Cloud Run vs Render:**
- ✅ **Não dorme** (vs 15min de sleep)
- ✅ **Mais confiável**
- ✅ **Melhor monitoramento**
- ✅ **Integração com Google Services**

## 🎯 **Resultado Final**

Depois do deploy no Cloud Run:
- ✅ **Bot funciona 24/7** com infraestrutura do Google
- ✅ **2 milhões de requisições gratuitas/mês**
- ✅ **Logs avançados** para debugging
- ✅ **Escala automaticamente** conforme uso
- ✅ **Integração perfeita** com Google Sheets/Calendar
- ✅ **Custo: R$ 0,00/mês** para uso pessoal

## 🚀 **Próximos Passos**

1. **Execute:** `./deploy-cloudrun.sh`
2. **Escaneie** QR code nos logs
3. **Teste** enviando mensagem no WhatsApp
4. **Monitore** uso no Google Cloud Console
5. **Aproveite** seu bot 24/7 gratuito!

**Cloud Run é a opção mais robusta e generosa para hospedagem gratuita! 🌟**
