# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# WhatsApp session data
.wwebjs_auth/
.wwebjs_cache/

# Google Service Account Keys
config/service-account-key.json
*.json
service-account-*.json

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory
coverage/

# nyc test coverage
.nyc_output/

# Dependency directories
jspm_packages/

# npm cache
.npm

# REPL history
.node_repl_history

# npm pack files
*.tgz

# Yarn
.yarn-integrity

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Local development artifacts
dist/
build/
