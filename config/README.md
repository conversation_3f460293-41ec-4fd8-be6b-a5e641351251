# Configuration Setup

This directory contains configuration files for the WhatsApp Todo Bot.

## Required Files

### service-account-key.json

This file contains your Google Cloud Service Account credentials. To set it up:

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the Google Sheets API
4. Create a Service Account:
   - Go to IAM & Admin > Service Accounts
   - Click "Create Service Account"
   - Give it a name like "whatsapp-todo-bot"
   - Grant it the "Editor" role (or create a custom role with Sheets access)
5. Create a key for the service account:
   - Click on the service account
   - Go to the "Keys" tab
   - Click "Add Key" > "Create new key"
   - Choose JSON format
   - Download the file and save it as `config/service-account-key.json`

### Google Sheets Setup

1. Create a new Google Sheet
2. Copy the Sheet ID from the URL (the long string between `/d/` and `/edit`)
3. Share the sheet with your service account email (found in the JSON file)
4. Give the service account "Editor" permissions
5. Add the Sheet ID to your `.env` file as `GOOGLE_SHEETS_ID`

## Security Notes

- Never commit the `service-account-key.json` file to version control
- Keep your OpenAI API key secure
- The `.gitignore` file is configured to exclude these sensitive files

## Environment Variables

Make sure to set all required environment variables in your `.env` file:

```
OPENAI_API_KEY=your_openai_api_key
GOOGLE_SHEETS_ID=your_google_sheets_id
GOOGLE_SERVICE_ACCOUNT_KEY_PATH=./config/service-account-key.json
```
