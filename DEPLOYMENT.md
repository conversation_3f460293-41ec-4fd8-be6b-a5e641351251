# Deployment Guide

This guide covers deploying the WhatsApp Todo Bot to various platforms.

## Local Development

For local development, simply follow the setup instructions in README.md.

## Production Deployment

### Option 1: VPS/Cloud Server (Recommended)

**Requirements:**
- Ubuntu 20.04+ or similar Linux distribution
- Node.js 18+
- PM2 for process management
- Nginx (optional, for reverse proxy)

**Setup Steps:**

1. **Server Setup:**
   ```bash
   # Update system
   sudo apt update && sudo apt upgrade -y
   
   # Install Node.js 18
   curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
   sudo apt-get install -y nodejs
   
   # Install PM2 globally
   sudo npm install -g pm2
   ```

2. **Deploy Application:**
   ```bash
   # Clone repository
   git clone <your-repo-url>
   cd WhatsappTodolist
   
   # Install dependencies
   npm install --production
   
   # Set up environment
   cp .env.example .env
   # Edit .env with your production values
   
   # Set up Google Service Account key
   # Upload your service-account-key.json to config/
   ```

3. **Start with PM2:**
   ```bash
   # Create PM2 ecosystem file
   cat > ecosystem.config.js << EOF
   module.exports = {
     apps: [{
       name: 'whatsapp-todo-bot',
       script: 'src/index.js',
       instances: 1,
       autorestart: true,
       watch: false,
       max_memory_restart: '1G',
       env: {
         NODE_ENV: 'production'
       }
     }]
   }
   EOF
   
   # Start application
   pm2 start ecosystem.config.js
   
   # Save PM2 configuration
   pm2 save
   pm2 startup
   ```

### Option 2: Docker Deployment

**Dockerfile:**
```dockerfile
FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy source code
COPY src/ ./src/
COPY config/ ./config/

# Create logs directory
RUN mkdir -p logs

# Expose port (if needed for health checks)
EXPOSE 3000

# Start application
CMD ["node", "src/index.js"]
```

**Docker Compose:**
```yaml
version: '3.8'
services:
  whatsapp-todo-bot:
    build: .
    restart: unless-stopped
    environment:
      - NODE_ENV=production
    env_file:
      - .env
    volumes:
      - ./logs:/app/logs
      - ./config:/app/config
      - ./.wwebjs_auth:/app/.wwebjs_auth
```

### Option 3: Railway/Heroku

**For Railway:**
1. Connect your GitHub repository
2. Set environment variables in Railway dashboard
3. Upload service account key as a file or encode as base64 in env var
4. Deploy

**For Heroku:**
1. Create Heroku app
2. Set config vars (environment variables)
3. Add buildpack: `heroku/nodejs`
4. Deploy via Git

## Environment Variables for Production

```bash
# Required
OPENAI_API_KEY=your_production_openai_key
GOOGLE_SHEETS_ID=your_production_sheets_id
GOOGLE_SERVICE_ACCOUNT_KEY_PATH=./config/service-account-key.json

# Optional Production Settings
NODE_ENV=production
LOG_LEVEL=info
DEBUG=false
DEFAULT_TIMEZONE=America/New_York
MAX_TODOS_PER_USER=100
```

## Security Considerations

1. **API Keys:**
   - Use environment variables, never hardcode
   - Rotate keys regularly
   - Use different keys for development and production

2. **Google Service Account:**
   - Use minimal required permissions
   - Regularly audit access
   - Consider using Workload Identity (GCP) for enhanced security

3. **Server Security:**
   - Keep system updated
   - Use firewall (UFW on Ubuntu)
   - Consider fail2ban for SSH protection
   - Use HTTPS if exposing any web interface

## Monitoring and Maintenance

### Health Checks

Add a simple health check endpoint:

```javascript
// Add to src/index.js
import express from 'express';

const app = express();
app.get('/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

app.listen(process.env.PORT || 3000);
```

### Log Management

```bash
# View logs with PM2
pm2 logs whatsapp-todo-bot

# Rotate logs
pm2 install pm2-logrotate

# Configure log rotation
pm2 set pm2-logrotate:max_size 10M
pm2 set pm2-logrotate:retain 30
```

### Backup Strategy

1. **Google Sheets:** Already serves as primary backup
2. **Configuration:** Backup .env and service account keys
3. **WhatsApp Session:** Backup .wwebjs_auth directory periodically

### Updates

```bash
# Pull latest changes
git pull origin main

# Install new dependencies
npm install --production

# Restart application
pm2 restart whatsapp-todo-bot
```

## Scaling Considerations

- **Single Instance:** Suitable for personal use or small teams
- **Multiple Instances:** Not recommended due to WhatsApp Web limitations
- **Load Balancing:** Not applicable for WhatsApp Web integration
- **Database:** Google Sheets handles up to 10 million cells

## Troubleshooting Production Issues

1. **Check PM2 status:** `pm2 status`
2. **View logs:** `pm2 logs whatsapp-todo-bot --lines 100`
3. **Restart application:** `pm2 restart whatsapp-todo-bot`
4. **Check system resources:** `htop` or `pm2 monit`
5. **Verify environment variables:** `pm2 env 0`

For more help, check the main README.md troubleshooting section.
